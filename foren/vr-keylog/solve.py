#!/usr/bin/env python3
"""
VR Keylog CTF Challenge Solver

This script analyzes VR controller tracking data to decode keystrokes.
The approach:
1. Parse the source data to build a mapping of controller positions to keys
2. Use this mapping to decode the unknown keystrokes
3. Apply the flag format rules (spaces -> underscores, double spaces -> braces)
"""

import re
import numpy as np
from typing import List, Tuple, Dict
import math

def parse_controller_data(filename: str) -> List[Tuple[float, str, Tuple[float, float, float], <PERSON>ple[float, float, float, float], str]]:
    """Parse controller tracking data from file."""
    data = []
    with open(filename, 'r') as f:
        lines = f.readlines()

    i = 1  # Skip header
    while i < len(lines):
        line = lines[i].strip()
        if not line:
            i += 1
            continue

        if line.startswith('Keystroke,'):
            # This is a keystroke line, get the previous controller data
            if i > 1:
                prev_line = lines[i-1].strip()
                if prev_line and not prev_line.startswith('Keystroke') and not prev_line.startswith('Answer'):
                    try:
                        # Parse controller data - need to handle CSV with parentheses carefully
                        # Format: timestamp,controller,"(x, y, z)","(qx, qy, qz, qw)",trigger

                        # Find the positions of parentheses to extract coordinates
                        pos_start = prev_line.find('(')
                        pos_end = prev_line.find(')', pos_start)
                        orient_start = prev_line.find('(', pos_end)
                        orient_end = prev_line.find(')', orient_start)

                        if pos_start == -1 or pos_end == -1 or orient_start == -1 or orient_end == -1:
                            i += 1
                            continue

                        # Extract parts before first parenthesis
                        prefix = prev_line[:pos_start].rstrip(',')
                        prefix_parts = prefix.split(',')

                        if len(prefix_parts) < 2:
                            i += 1
                            continue

                        timestamp = float(prefix_parts[0])
                        controller = prefix_parts[1]

                        # Extract position
                        pos_str = prev_line[pos_start+1:pos_end]
                        pos_parts = [float(x.strip()) for x in pos_str.split(',')]
                        if len(pos_parts) != 3:
                            i += 1
                            continue
                        position = tuple(pos_parts)

                        # Extract orientation
                        orient_str = prev_line[orient_start+1:orient_end]
                        orient_parts = [float(x.strip()) for x in orient_str.split(',')]
                        if len(orient_parts) != 4:
                            i += 1
                            continue
                        orientation = tuple(orient_parts)

                        # Extract trigger (after last parenthesis)
                        suffix = prev_line[orient_end+1:].lstrip(',')
                        trigger = suffix.strip() == 'True'

                        # Parse keystroke
                        keystroke_parts = line.split(',')
                        if len(keystroke_parts) >= 2:
                            key = keystroke_parts[1].strip()

                            data.append((timestamp, controller, position, orientation, key))
                    except (ValueError, IndexError) as e:
                        print(f"Error parsing line {i}: {prev_line}")
                        print(f"Error: {e}")
        i += 1

    return data

def quaternion_to_forward_vector(quat: Tuple[float, float, float, float]) -> Tuple[float, float, float]:
    """Convert quaternion to forward vector (along z-axis)."""
    x, y, z, w = quat
    
    # Forward vector in VR is typically along negative z-axis
    # Rotate the vector (0, 0, -1) by the quaternion
    forward_x = 2 * (x*z + w*y)
    forward_y = 2 * (y*z - w*x)
    forward_z = 2 * (z*z + w*w) - 1
    
    return (forward_x, forward_y, forward_z)

def calculate_keyboard_position(controller_pos: Tuple[float, float, float], 
                              controller_orient: Tuple[float, float, float, float],
                              distance: float = 2.0) -> Tuple[float, float, float]:
    """Calculate the position on the virtual keyboard based on controller position and orientation."""
    forward = quaternion_to_forward_vector(controller_orient)
    
    # The keyboard is 2 units in front of the controller along its forward vector
    keyboard_x = controller_pos[0] + forward[0] * distance
    keyboard_y = controller_pos[1] + forward[1] * distance
    keyboard_z = controller_pos[2] + forward[2] * distance
    
    return (keyboard_x, keyboard_y, keyboard_z)

def build_keyboard_layout(source_data: List) -> Dict[str, Tuple[float, float, float]]:
    """Build a mapping of keys to their average positions on the virtual keyboard."""
    key_positions = {}
    
    for timestamp, controller, pos, orient, key in source_data:
        if key not in ['Space_Button', 'Enter_Button']:
            keyboard_pos = calculate_keyboard_position(pos, orient)
            
            if key not in key_positions:
                key_positions[key] = []
            key_positions[key].append(keyboard_pos)
    
    # Calculate average position for each key
    avg_positions = {}
    for key, positions in key_positions.items():
        avg_x = sum(p[0] for p in positions) / len(positions)
        avg_y = sum(p[1] for p in positions) / len(positions)
        avg_z = sum(p[2] for p in positions) / len(positions)
        avg_positions[key] = (avg_x, avg_y, avg_z)
    
    return avg_positions

def find_closest_key(target_pos: Tuple[float, float, float], 
                    key_layout: Dict[str, Tuple[float, float, float]]) -> str:
    """Find the closest key to the target position."""
    min_distance = float('inf')
    closest_key = None
    
    for key, pos in key_layout.items():
        distance = math.sqrt(
            (target_pos[0] - pos[0])**2 + 
            (target_pos[1] - pos[1])**2 + 
            (target_pos[2] - pos[2])**2
        )
        
        if distance < min_distance:
            min_distance = distance
            closest_key = key
    
    return closest_key

def normalize_coordinates(positions: List[Tuple[float, float, float]]) -> List[Tuple[float, float, float]]:
    """Normalize coordinates to 0-1 range based on min/max values."""
    if not positions:
        return []

    x_vals = [p[0] for p in positions]
    y_vals = [p[1] for p in positions]
    z_vals = [p[2] for p in positions]

    x_min, x_max = min(x_vals), max(x_vals)
    y_min, y_max = min(y_vals), max(y_vals)
    z_min, z_max = min(z_vals), max(z_vals)

    # Avoid division by zero
    x_range = x_max - x_min if x_max != x_min else 1
    y_range = y_max - y_min if y_max != y_min else 1
    z_range = z_max - z_min if z_max != z_min else 1

    normalized = []
    for x, y, z in positions:
        norm_x = (x - x_min) / x_range
        norm_y = (y - y_min) / y_range
        norm_z = (z - z_min) / z_range
        normalized.append((norm_x, norm_y, norm_z))

    return normalized

def build_qwerty_layout() -> Dict[str, Tuple[int, int]]:
    """Build a QWERTY keyboard layout with grid coordinates."""
    layout = {
        # Top row (y=0)
        'q': (0, 0), 'w': (1, 0), 'e': (2, 0), 'r': (3, 0), 't': (4, 0),
        'y': (5, 0), 'u': (6, 0), 'i': (7, 0), 'o': (8, 0), 'p': (9, 0),

        # Middle row (y=1)
        'a': (0, 1), 's': (1, 1), 'd': (2, 1), 'f': (3, 1), 'g': (4, 1),
        'h': (5, 1), 'j': (6, 1), 'k': (7, 1), 'l': (8, 1), "'": (9, 1),

        # Bottom row (y=2)
        'z': (0, 2), 'x': (1, 2), 'c': (2, 2), 'v': (3, 2), 'b': (4, 2),
        'n': (5, 2), 'm': (6, 2), ',': (7, 2), '.': (8, 2),
    }
    return layout

def map_positions_to_grid(source_data: List) -> Dict[str, Tuple[float, float]]:
    """Map keyboard positions to 2D grid coordinates."""
    key_positions = {}

    # Collect all keyboard positions for each key
    for timestamp, controller, pos, orient, key in source_data:
        if key not in ['Space_Button', 'Enter_Button']:
            keyboard_pos = calculate_keyboard_position(pos, orient)

            if key not in key_positions:
                key_positions[key] = []
            key_positions[key].append(keyboard_pos)

    # Calculate average position for each key
    avg_positions = {}
    for key, positions in key_positions.items():
        avg_x = sum(p[0] for p in positions) / len(positions)
        avg_y = sum(p[1] for p in positions) / len(positions)
        avg_z = sum(p[2] for p in positions) / len(positions)
        avg_positions[key] = (avg_x, avg_y, avg_z)

    # Convert to 2D by projecting onto the keyboard plane (use X and Z coordinates)
    grid_positions = {}
    for key, (x, y, z) in avg_positions.items():
        grid_positions[key] = (x, z)  # Use X and Z as the 2D coordinates

    return grid_positions

def find_closest_key_2d(target_pos: Tuple[float, float],
                       key_positions: Dict[str, Tuple[float, float]]) -> str:
    """Find the closest key to the target 2D position."""
    min_distance = float('inf')
    closest_key = None

    for key, pos in key_positions.items():
        distance = math.sqrt(
            (target_pos[0] - pos[0])**2 +
            (target_pos[1] - pos[1])**2
        )

        if distance < min_distance:
            min_distance = distance
            closest_key = key

    return closest_key

def transform_coordinates(source_positions: Dict, unknown_positions: List[Tuple[float, float]]) -> List[Tuple[float, float]]:
    """Transform unknown coordinates to match source coordinate system."""
    if not unknown_positions or not source_positions:
        return unknown_positions

    # Get source coordinate ranges
    source_x_vals = [pos[0] for pos in source_positions.values()]
    source_z_vals = [pos[1] for pos in source_positions.values()]
    source_x_min, source_x_max = min(source_x_vals), max(source_x_vals)
    source_z_min, source_z_max = min(source_z_vals), max(source_z_vals)

    # Get unknown coordinate ranges
    unknown_x_vals = [pos[0] for pos in unknown_positions]
    unknown_z_vals = [pos[1] for pos in unknown_positions]
    unknown_x_min, unknown_x_max = min(unknown_x_vals), max(unknown_x_vals)
    unknown_z_min, unknown_z_max = min(unknown_z_vals), max(unknown_z_vals)

    # Calculate transformation parameters
    x_scale = (source_x_max - source_x_min) / (unknown_x_max - unknown_x_min) if unknown_x_max != unknown_x_min else 1
    z_scale = (source_z_max - source_z_min) / (unknown_z_max - unknown_z_min) if unknown_z_max != unknown_z_min else 1

    # Transform unknown positions to source coordinate system
    transformed = []
    for x, z in unknown_positions:
        # Normalize to 0-1 range
        norm_x = (x - unknown_x_min) / (unknown_x_max - unknown_x_min) if unknown_x_max != unknown_x_min else 0
        norm_z = (z - unknown_z_min) / (unknown_z_max - unknown_z_min) if unknown_z_max != unknown_z_min else 0

        # Scale to source range
        transformed_x = source_x_min + norm_x * (source_x_max - source_x_min)
        transformed_z = source_z_min + norm_z * (source_z_max - source_z_min)

        transformed.append((transformed_x, transformed_z))

    return transformed

def decode_unknown_keystrokes_with_timing(unknown_data: List, source_grid_positions: Dict) -> str:
    """Decode unknown keystrokes using coordinate transformation and timing analysis."""
    decoded_chars = []
    timestamps = []

    # Get unknown keyboard positions, timestamps, and convert to 2D
    unknown_positions_2d = []
    for timestamp, controller, pos, orient, key in unknown_data:
        if key == 'Unknown':
            keyboard_pos = calculate_keyboard_position(pos, orient)
            # Project to 2D using X and Z coordinates
            unknown_positions_2d.append((keyboard_pos[0], keyboard_pos[2]))
            timestamps.append(timestamp)

    print(f"Unknown positions (first 5): {unknown_positions_2d[:5]}")
    print(f"Timestamps (first 5): {timestamps[:5]}")

    # Transform unknown positions to match source coordinate system
    transformed_positions = transform_coordinates(source_grid_positions, unknown_positions_2d)

    print(f"Transformed positions (first 5): {transformed_positions[:5]}")

    # Analyze timing gaps to detect spaces
    time_gaps = []
    for i in range(1, len(timestamps)):
        gap = timestamps[i] - timestamps[i-1]
        time_gaps.append(gap)

    if time_gaps:
        avg_gap = sum(time_gaps) / len(time_gaps)
        print(f"Average time gap: {avg_gap:.3f}")
        print(f"Time gaps: {[f'{gap:.3f}' for gap in time_gaps[:10]]}")

        # Identify large gaps (potential spaces)
        space_threshold = avg_gap * 2  # Gaps larger than 2x average might be spaces
        double_space_threshold = avg_gap * 4  # Even larger gaps might be double spaces

        print(f"Space threshold: {space_threshold:.3f}")
        print(f"Double space threshold: {double_space_threshold:.3f}")

    # Find closest keys and insert spaces based on timing
    for i, pos_2d in enumerate(transformed_positions):
        closest_key = find_closest_key_2d(pos_2d, source_grid_positions)
        decoded_chars.append(closest_key)

        # Check if we need to add a space after this character
        if i < len(time_gaps):
            gap = time_gaps[i]
            if time_gaps and gap > double_space_threshold:
                decoded_chars.append('  ')  # Double space
            elif time_gaps and gap > space_threshold:
                decoded_chars.append(' ')   # Single space

    return ''.join(decoded_chars)

def decode_unknown_keystrokes_normalized(unknown_data: List, normalized_key_layout: Dict) -> str:
    """Decode unknown keystrokes using normalized coordinates."""
    decoded_chars = []

    # Get all unknown keyboard positions
    unknown_positions = []
    for timestamp, controller, pos, orient, key in unknown_data:
        if key == 'Unknown':
            keyboard_pos = calculate_keyboard_position(pos, orient)
            unknown_positions.append(keyboard_pos)

    # Normalize unknown positions
    normalized_unknown = normalize_coordinates(unknown_positions)

    # Find closest keys
    for norm_pos in normalized_unknown:
        closest_key = find_closest_key(norm_pos, normalized_key_layout)
        decoded_chars.append(closest_key)

    return ''.join(decoded_chars)

def format_flag(decoded_text: str) -> str:
    """Apply flag formatting rules: single space -> underscore, double space -> braces."""
    # First, let's see what we have
    print(f"Raw decoded text: '{decoded_text}'")
    
    # The hint mentions spaces, but we might need to detect patterns
    # Let's try different approaches
    
    # If there are actual space characters, apply the rules
    if ' ' in decoded_text:
        # Replace double spaces with braces
        formatted = decoded_text.replace('  ', '{}')
        # Replace single spaces with underscores
        formatted = formatted.replace(' ', '_')
        return formatted
    
    # If no spaces, maybe we need to infer them from the data
    return decoded_text

def analyze_coordinate_ranges(data: List, label: str):
    """Analyze the coordinate ranges in the data."""
    print(f"\n{label} coordinate analysis:")

    controller_positions = []
    keyboard_positions = []

    for timestamp, controller, pos, orient, key in data:
        controller_positions.append(pos)
        if key != 'Unknown':  # For source data
            keyboard_pos = calculate_keyboard_position(pos, orient)
            keyboard_positions.append(keyboard_pos)
        elif key == 'Unknown':  # For unknown data
            keyboard_pos = calculate_keyboard_position(pos, orient)
            keyboard_positions.append(keyboard_pos)

    if controller_positions:
        x_vals = [p[0] for p in controller_positions]
        y_vals = [p[1] for p in controller_positions]
        z_vals = [p[2] for p in controller_positions]

        print(f"  Controller positions:")
        print(f"    X: {min(x_vals):.3f} to {max(x_vals):.3f}")
        print(f"    Y: {min(y_vals):.3f} to {max(y_vals):.3f}")
        print(f"    Z: {min(z_vals):.3f} to {max(z_vals):.3f}")

    if keyboard_positions:
        x_vals = [p[0] for p in keyboard_positions]
        y_vals = [p[1] for p in keyboard_positions]
        z_vals = [p[2] for p in keyboard_positions]

        print(f"  Keyboard positions:")
        print(f"    X: {min(x_vals):.3f} to {max(x_vals):.3f}")
        print(f"    Y: {min(y_vals):.3f} to {max(y_vals):.3f}")
        print(f"    Z: {min(z_vals):.3f} to {max(z_vals):.3f}")

def build_sequence_based_layout(source_data: List) -> Dict[str, int]:
    """Build keyboard layout based on the typing sequence from source data."""
    # The known sequence from source.txt
    sequence = "qwertyuiopasdfghjkl'zxcvbnm,..,mnbvcxz'lkjhgfdspoiuytrewq"

    # Map each character to its position in the sequence
    char_to_sequence_pos = {}
    for i, char in enumerate(sequence):
        if char not in char_to_sequence_pos:  # Take first occurrence
            char_to_sequence_pos[char] = i

    return char_to_sequence_pos

def decode_using_sequence_order(unknown_data: List, source_data: List) -> str:
    """Decode using the order of characters in the typing sequence."""
    # Get the sequence-based layout
    sequence_layout = build_sequence_based_layout(source_data)

    # Get positions for each character in source data
    source_positions = {}
    for timestamp, controller, pos, orient, key in source_data:
        if key not in ['Space_Button', 'Enter_Button'] and key in sequence_layout:
            keyboard_pos = calculate_keyboard_position(pos, orient)
            if key not in source_positions:
                source_positions[key] = []
            source_positions[key].append(keyboard_pos)

    # Calculate average position for each key
    avg_source_positions = {}
    for key, positions in source_positions.items():
        avg_x = sum(p[0] for p in positions) / len(positions)
        avg_y = sum(p[1] for p in positions) / len(positions)
        avg_z = sum(p[2] for p in positions) / len(positions)
        avg_source_positions[key] = (avg_x, avg_y, avg_z)

    # Get unknown positions
    unknown_positions = []
    for timestamp, controller, pos, orient, key in unknown_data:
        if key == 'Unknown':
            keyboard_pos = calculate_keyboard_position(pos, orient)
            unknown_positions.append(keyboard_pos)

    # Try to match unknown positions to source positions
    decoded_chars = []
    for unknown_pos in unknown_positions:
        min_distance = float('inf')
        closest_key = None

        for key, source_pos in avg_source_positions.items():
            distance = math.sqrt(
                (unknown_pos[0] - source_pos[0])**2 +
                (unknown_pos[1] - source_pos[1])**2 +
                (unknown_pos[2] - source_pos[2])**2
            )

            if distance < min_distance:
                min_distance = distance
                closest_key = key

        if closest_key:
            decoded_chars.append(closest_key)

    return ''.join(decoded_chars)

def try_coordinate_space_mapping(unknown_data: List, source_data: List) -> List[str]:
    """Try mapping unknown coordinates to source coordinate space using different methods."""
    results = []

    # Method 1: Direct 3D mapping
    decoded_3d = decode_using_sequence_order(unknown_data, source_data)
    results.append(("3D Direct", decoded_3d))
    print(f"3D Direct: '{decoded_3d}'")

    # Method 2: Try different coordinate projections and transformations
    # Get source and unknown positions
    source_positions = {}
    for timestamp, controller, pos, orient, key in source_data:
        if key not in ['Space_Button', 'Enter_Button']:
            keyboard_pos = calculate_keyboard_position(pos, orient)
            if key not in source_positions:
                source_positions[key] = []
            source_positions[key].append(keyboard_pos)

    # Average source positions
    avg_source_positions = {}
    for key, positions in source_positions.items():
        avg_x = sum(p[0] for p in positions) / len(positions)
        avg_z = sum(p[2] for p in positions) / len(positions)  # Use X,Z projection
        avg_source_positions[key] = (avg_x, avg_z)

    # Get unknown positions (X,Z projection)
    unknown_positions_xz = []
    for timestamp, controller, pos, orient, key in unknown_data:
        if key == 'Unknown':
            keyboard_pos = calculate_keyboard_position(pos, orient)
            unknown_positions_xz.append((keyboard_pos[0], keyboard_pos[2]))

    # Try different transformations
    transformations = [
        ("XZ Normal", lambda x, z: (x, z)),
        ("XZ Flip X", lambda x, z: (-x, z)),
        ("XZ Flip Z", lambda x, z: (x, -z)),
        ("XZ Flip both", lambda x, z: (-x, -z)),
    ]

    for name, transform_func in transformations:
        # Apply transformation
        transformed_positions = [transform_func(x, z) for x, z in unknown_positions_xz]

        # Normalize to source coordinate range
        final_positions = transform_coordinates(avg_source_positions, transformed_positions)

        # Decode
        decoded_chars = []
        for pos_2d in final_positions:
            closest_key = find_closest_key_2d(pos_2d, avg_source_positions)
            decoded_chars.append(closest_key)

        decoded_text = ''.join(decoded_chars)
        results.append((name, decoded_text))
        print(f"{name}: '{decoded_text}'")

    return results

def analyze_timing_patterns(unknown_data: List):
    """Analyze timing patterns to identify potential word boundaries."""
    timestamps = []
    for timestamp, controller, pos, orient, key in unknown_data:
        if key == 'Unknown':
            timestamps.append(timestamp)

    if len(timestamps) < 2:
        return

    gaps = []
    for i in range(1, len(timestamps)):
        gap = timestamps[i] - timestamps[i-1]
        gaps.append(gap)

    print(f"\nTiming analysis:")
    print(f"Total keystrokes: {len(timestamps)}")
    print(f"Average gap: {sum(gaps)/len(gaps):.3f} seconds")
    print(f"Min gap: {min(gaps):.3f} seconds")
    print(f"Max gap: {max(gaps):.3f} seconds")

    # Look for unusually large gaps
    avg_gap = sum(gaps) / len(gaps)
    large_gaps = [(i, gap) for i, gap in enumerate(gaps) if gap > avg_gap * 1.5]

    if large_gaps:
        print(f"Large gaps (potential word boundaries):")
        for i, gap in large_gaps:
            print(f"  After keystroke {i+1}: {gap:.3f}s")

def try_manual_qwerty_mapping(unknown_data: List, source_data: List) -> str:
    """Try mapping based on QWERTY keyboard layout knowledge."""

    # Standard QWERTY layout
    qwerty_layout = {
        # Row 1 (top)
        'q': (0, 0), 'w': (1, 0), 'e': (2, 0), 'r': (3, 0), 't': (4, 0),
        'y': (5, 0), 'u': (6, 0), 'i': (7, 0), 'o': (8, 0), 'p': (9, 0),
        # Row 2 (middle)
        'a': (0, 1), 's': (1, 1), 'd': (2, 1), 'f': (3, 1), 'g': (4, 1),
        'h': (5, 1), 'j': (6, 1), 'k': (7, 1), 'l': (8, 1), "'": (9, 1),
        # Row 3 (bottom)
        'z': (0, 2), 'x': (1, 2), 'c': (2, 2), 'v': (3, 2), 'b': (4, 2),
        'n': (5, 2), 'm': (6, 2), ',': (7, 2), '.': (8, 2)
    }

    # Get source positions and map to QWERTY grid
    source_positions = {}
    for timestamp, controller, pos, orient, key in source_data:
        if key in qwerty_layout:
            keyboard_pos = calculate_keyboard_position(pos, orient)
            if key not in source_positions:
                source_positions[key] = []
            source_positions[key].append(keyboard_pos)

    # Average source positions
    avg_source_positions = {}
    for key, positions in source_positions.items():
        avg_x = sum(p[0] for p in positions) / len(positions)
        avg_y = sum(p[1] for p in positions) / len(positions)
        avg_z = sum(p[2] for p in positions) / len(positions)
        avg_source_positions[key] = (avg_x, avg_y, avg_z)

    # Map source positions to QWERTY grid coordinates
    source_to_qwerty = {}
    for key, pos_3d in avg_source_positions.items():
        if key in qwerty_layout:
            source_to_qwerty[key] = qwerty_layout[key]

    print(f"Mapped {len(source_to_qwerty)} keys to QWERTY positions")

    # Get unknown positions
    unknown_positions = []
    for timestamp, controller, pos, orient, key in unknown_data:
        if key == 'Unknown':
            keyboard_pos = calculate_keyboard_position(pos, orient)
            unknown_positions.append(keyboard_pos)

    # Try to find the best transformation from unknown space to source space
    # This is a complex problem, so let's try a simpler approach:
    # Just find the closest source key for each unknown position

    decoded_chars = []
    for unknown_pos in unknown_positions:
        min_distance = float('inf')
        closest_key = None

        for key, source_pos in avg_source_positions.items():
            # Try different distance metrics
            distance = math.sqrt(
                (unknown_pos[0] - source_pos[0])**2 +
                (unknown_pos[1] - source_pos[1])**2 +
                (unknown_pos[2] - source_pos[2])**2
            )

            if distance < min_distance:
                min_distance = distance
                closest_key = key

        if closest_key:
            decoded_chars.append(closest_key)

    return ''.join(decoded_chars)

def main():
    print("VR Keylog CTF Challenge Solver")
    print("=" * 40)

    # Parse source data (known keystrokes)
    print("Parsing source data...")
    source_data = parse_controller_data('source.txt')
    print(f"Found {len(source_data)} known keystrokes")

    # Parse unknown data
    print("\nParsing unknown data...")
    unknown_data = parse_controller_data('unknown.txt')
    print(f"Found {len(unknown_data)} unknown keystrokes")

    # Analyze timing patterns
    analyze_timing_patterns(unknown_data)

    # Try manual QWERTY mapping
    print("\nTrying manual QWERTY mapping...")
    manual_result = try_manual_qwerty_mapping(unknown_data, source_data)
    print(f"Manual QWERTY result: '{manual_result}'")

    # Try coordinate space mapping
    print("\nTrying coordinate space mapping:")
    results = try_coordinate_space_mapping(unknown_data, source_data)

    # Look for the most promising result
    print("\nAnalyzing all results for flag-like patterns...")
    all_results = [("Manual QWERTY", manual_result)] + results

    for name, decoded_text in all_results:
        # Check if it looks like it could be a flag
        if len(set(decoded_text)) > 5:  # Has reasonable character diversity
            flag = format_flag(decoded_text)
            print(f"{name} formatted: {flag}")
            if not flag.startswith('tjctf'):
                flag_with_prefix = f"tjctf{{{flag}}}"
                print(f"{name} with prefix: {flag_with_prefix}")

            # Try reversed
            reversed_text = decoded_text[::-1]
            reversed_flag = format_flag(reversed_text)
            print(f"{name} reversed: {reversed_flag}")
            if not reversed_flag.startswith('tjctf'):
                reversed_flag_with_prefix = f"tjctf{{{reversed_flag}}}"
                print(f"{name} reversed with prefix: {reversed_flag_with_prefix}")

    # Let's also try some manual analysis of the most promising result
    print("\nManual analysis of 'XZ Flip X' result:")
    flip_x_result = "y.wuyaa.amqreattwkreaetwtlqmwtesa"
    print(f"Original: {flip_x_result}")
    print(f"Reversed: {flip_x_result[::-1]}")

    # Try to identify potential word boundaries
    print("Looking for potential words...")
    # Common English patterns
    if 'the' in flip_x_result:
        print("Found 'the'")
    if 'and' in flip_x_result:
        print("Found 'and'")
    if 'are' in flip_x_result:
        print("Found 'are'")
    if 'rea' in flip_x_result:
        print("Found 'rea' (could be part of 'real', 'read', etc.)")

    # Try splitting on repeated patterns
    parts = flip_x_result.split('rea')
    if len(parts) > 1:
        print(f"Split on 'rea': {parts}")

    # Check if it contains common flag words
    flag_words = ['flag', 'key', 'secret', 'password', 'code', 'vr', 'virtual', 'reality']
    for word in flag_words:
        if word in flip_x_result.lower():
            print(f"Found potential flag word: {word}")

    print("\nTrying to manually decode the most promising result...")
    print("If you see any patterns or potential words, try submitting variations of:")
    print(f"tjctf{{{flip_x_result}}}")
    print(f"tjctf{{{flip_x_result[::-1]}}}")

    # Try with spaces inserted at different positions
    print("\nTrying with spaces at different positions:")
    for i in range(5, len(flip_x_result), 5):
        spaced = flip_x_result[:i] + ' ' + flip_x_result[i:]
        print(f"  {spaced}")

    # Apply the hint about spaces and braces more systematically
    print("\nApplying hint about spaces and braces systematically:")

    # The hint says: "Single spaces represent underscores and double spaces represent braces"
    # So we need to find what represents spaces in our decoded text

    candidates = [flip_x_result, flip_x_result[::-1]]  # Try both normal and reversed

    for i, candidate in enumerate(candidates):
        direction = "normal" if i == 0 else "reversed"
        print(f"\nTrying {direction}: {candidate}")

        # Try different patterns as space indicators
        space_patterns = ['aa', 'tw', 'rea', 'wt', 'ea', 'qm', '..', 'tt']

        for pattern in space_patterns:
            if pattern in candidate:
                # Replace pattern with space, then apply the flag formatting rules
                with_spaces = candidate.replace(pattern, ' ')
                # Apply flag rules: single space -> underscore, double space -> braces
                formatted = with_spaces.replace('  ', '{}').replace(' ', '_')
                print(f"  Pattern '{pattern}' as space: {formatted}")

                # Check if this looks like a reasonable flag
                if len(formatted) > 10 and ('_' in formatted or '{}' in formatted):
                    flag_candidate = f"tjctf{{{formatted}}}"
                    print(f"    -> Flag candidate: {flag_candidate}")

    # Try a different approach - what if the text is already correct but needs different interpretation?
    print(f"\nDirect flag attempts:")
    print(f"tjctf{{{flip_x_result}}}")
    print(f"tjctf{{{flip_x_result[::-1]}}}")

    # Try removing dots and apostrophes
    cleaned = flip_x_result.replace('.', '').replace("'", '')
    print(f"tjctf{{{cleaned}}}")

    # Try interpreting dots as spaces
    with_dot_spaces = flip_x_result.replace('.', '_')
    print(f"tjctf{{{with_dot_spaces}}}")

    print("\nMost promising candidates to try:")
    print("1. tjctf{y.wuyaa.amqreattwkreaetwtlqmwtesa}")
    print("2. tjctf{asetwmqltwteaerkwttaerqma.aayuw.y}")
    print("3. tjctf{y_wuyaa_amqreattwkreaetwtlqmwtesa}")
    print("4. tjctf{ywuyaaamqreattwkreaetwtlqmwtesa}")

    # Let's also try the other promising results
    other_results = ["txotto'xovoii'yycoguioiuoydpvoyio'", "hqlgh''q'ekm'hjqmrjklmjmhd.emjml'"]
    for result in other_results:
        cleaned = result.replace("'", "").replace(".", "")
        print(f"5. tjctf{{{cleaned}}}")
        with_underscores = result.replace("'", "_").replace(".", "_")
        print(f"6. tjctf{{{with_underscores}}}")

if __name__ == "__main__":
    main()
