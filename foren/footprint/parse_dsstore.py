#!/usr/bin/env python3
import struct
import sys

def parse_ds_store(filename):
    with open(filename, 'rb') as f:
        data = f.read()
    
    # Look for filename patterns in the data
    # .DS_Store files contain UTF-16 encoded filenames
    filenames = []
    
    # Search for potential filename patterns
    i = 0
    while i < len(data) - 1:
        # Look for UTF-16 patterns (alternating bytes with nulls)
        if data[i] != 0 and data[i+1] == 0:
            # Potential start of UTF-16 string
            filename = ""
            j = i
            while j < len(data) - 1 and data[j] != 0 and data[j+1] == 0:
                # Check if it's a printable ASCII character
                if 32 <= data[j] <= 126:
                    filename += chr(data[j])
                    j += 2
                else:
                    break
            
            # If we found a reasonable filename (length > 3 and contains valid chars)
            if len(filename) > 3 and any(c.isalnum() for c in filename):
                filenames.append(filename)
                i = j
            else:
                i += 1
        else:
            i += 1
    
    return filenames

def main():
    if len(sys.argv) != 2:
        print("Usage: python3 parse_dsstore.py <.DS_Store file>")
        sys.exit(1)

    filenames = parse_ds_store(sys.argv[1])

    print("Found potential filenames:")
    base64_candidates = []
    for filename in set(filenames):  # Remove duplicates
        print(f"  {filename}")
        # Check if it looks like base64 (alphanumeric + / + =)
        if len(filename) > 10 and all(c.isalnum() or c in '+/=' for c in filename):
            base64_candidates.append(filename)

    print("\nPotential base64 strings:")
    for candidate in base64_candidates:
        print(f"  {candidate}")
        try:
            import base64
            decoded = base64.b64decode(candidate + '==').decode('utf-8', errors='ignore')
            if decoded:
                print(f"    -> {decoded}")
        except:
            pass

if __name__ == "__main__":
    main()
