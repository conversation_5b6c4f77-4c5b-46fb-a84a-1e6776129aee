#!/usr/bin/env python3

import struct
from scapy.all import *

def extract_usbi_data(pcap_file):
    """Extract data from USBI packets and reconstruct the PNG file"""
    packets = rdpcap(pcap_file)
    usbi_data = {}
    
    for packet in packets:
        if packet.haslayer(Raw):
            payload = bytes(packet[Raw])
            
            # Look for USBI header
            if payload.startswith(b'USBI'):
                # USBI header format: USBI + sequence_number (2 bytes) + unknown (2 bytes) + length (4 bytes) + data
                if len(payload) >= 12:
                    seq_num = struct.unpack('>H', payload[4:6])[0]  # Big endian 2-byte sequence
                    unknown = struct.unpack('>H', payload[6:8])[0]  # Unknown 2 bytes
                    length = struct.unpack('>I', payload[8:12])[0]  # Big endian 4-byte length
                    data = payload[12:12+length]

                    print(f"Found USBI packet: seq={seq_num}, unknown={unknown}, length={length}, data_len={len(data)}")
                    usbi_data[seq_num] = data
    
    # Reconstruct the file by concatenating data in sequence order
    reconstructed_data = b''
    for seq_num in sorted(usbi_data.keys()):
        reconstructed_data += usbi_data[seq_num]
        print(f"Added sequence {seq_num}: {len(usbi_data[seq_num])} bytes")
    
    return reconstructed_data

def main():
    pcap_file = 'chall.pcapng'
    
    print("Extracting USBI data from packets...")
    png_data = extract_usbi_data(pcap_file)
    
    print(f"\nTotal reconstructed data: {len(png_data)} bytes")
    
    # Check if it starts with PNG header
    if png_data.startswith(b'\x89PNG'):
        print("✓ Data starts with PNG header!")
        
        # Save the PNG file
        with open('reconstructed.png', 'wb') as f:
            f.write(png_data)
        print("✓ Saved as reconstructed.png")
        
        # Check if it ends with PNG footer
        if png_data.endswith(b'IEND\xae\x42\x60\x82'):
            print("✓ Data ends with PNG footer!")
        else:
            print("⚠ Data doesn't end with PNG footer")
            print(f"Last 20 bytes: {png_data[-20:].hex()}")
    else:
        print("⚠ Data doesn't start with PNG header")
        print(f"First 20 bytes: {png_data[:20].hex()}")

if __name__ == '__main__':
    main()
