import wave
from PIL import Image
import numpy as np

# Load the image
img = Image.open('albumcover.png')
img = np.array(img)

# Reverse the encoding process
img = img.astype(np.float64)
img = img / 255 * 65535 - 32767
img = img.astype(np.int16)

# Flatten to 1D
frames = img.flatten()

# Save as WAV
with wave.open('flag.wav', 'wb') as w:
    w.setnchannels(1)
    w.setsampwidth(2) 
    w.setframerate(44100)
    w.writeframes(frames.tobytes())
