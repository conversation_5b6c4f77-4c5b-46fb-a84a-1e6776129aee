# Let me try to actually solve this properly with an efficient algorithm
# The key insight is to use precomputed factorials and modular arithmetic

f = [x.strip() for x in open("chall.txt").read().split('\n')]
n = int(f[0])
a = list(map(int, f[1].split()))
m = 998244353

print(f"n = {n}, numbers = {len(a)}")

# Precompute factorials up to n
print("Precomputing factorials...")
fact = [1] * (n + 1)
for i in range(1, n + 1):
    fact[i] = (fact[i-1] * i) % m

print("Precomputing inverse factorials...")
inv_fact = [1] * (n + 1)
inv_fact[n] = pow(fact[n], m - 2, m)  # <PERSON><PERSON><PERSON>'s little theorem
for i in range(n - 1, -1, -1):
    inv_fact[i] = (inv_fact[i + 1] * (i + 1)) % m

def choose(n, r):
    """Efficient combination using precomputed factorials"""
    if r > n or r < 0:
        return 0
    return (fact[n] * inv_fact[r] % m) * inv_fact[n - r] % m

print("Computing the product...")
ans = 1
for i, x in enumerate(a):
    if i % 50000 == 0:
        print(f"Progress: {i}/{len(a)}")

    result = choose(n, x)
    ans = (ans * result) % m

    # Early termination if we hit 0
    if ans == 0:
        print(f"Product became 0 at position {i} with value {x}")
        break

print(f"tjctf{{{ans}}}")