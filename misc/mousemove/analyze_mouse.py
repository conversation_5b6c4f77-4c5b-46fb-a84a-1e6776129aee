#!/usr/bin/env python3

import matplotlib.pyplot as plt
import numpy as np

def parse_coordinates(filename):
    """Parse the mouse movement coordinates from the file."""
    coordinates = []
    
    with open(filename, 'r') as f:
        for line in f:
            line = line.strip()
            if ',' in line:
                try:
                    x, y = map(int, line.split(','))
                    coordinates.append((x, y))
                except ValueError:
                    continue
    
    return coordinates

def analyze_coordinates(coords):
    """Analyze the coordinate data to understand the canvas dimensions."""
    if not coords:
        return
    
    x_coords = [x for x, y in coords]
    y_coords = [y for x, y in coords]
    
    print(f"Total coordinates: {len(coords)}")
    print(f"X range: {min(x_coords)} to {max(x_coords)}")
    print(f"Y range: {min(y_coords)} to {max(y_coords)}")
    print(f"Canvas dimensions: {max(x_coords) - min(x_coords)} x {max(y_coords) - min(y_coords)}")

def plot_mouse_trail(coords, save_path="mouse_trail.png"):
    """Plot the mouse trail to visualize what was drawn."""
    if not coords:
        print("No coordinates to plot")
        return
    
    x_coords = [x for x, y in coords]
    y_coords = [y for x, y in coords]
    
    # Create the plot
    plt.figure(figsize=(16, 10))
    
    # Plot as a scatter plot to see individual points
    plt.scatter(x_coords, y_coords, s=1, alpha=0.6, c='blue')
    
    # Invert y-axis since screen coordinates have origin at top-left
    plt.gca().invert_yaxis()
    
    plt.title("Mouse Movement Trail")
    plt.xlabel("X coordinate")
    plt.ylabel("Y coordinate")
    plt.grid(True, alpha=0.3)
    
    # Save the plot
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"Plot saved as {save_path}")

def plot_connected_trail(coords, save_path="mouse_trail_connected.png"):
    """Plot the mouse trail with connected lines."""
    if not coords:
        print("No coordinates to plot")
        return
    
    x_coords = [x for x, y in coords]
    y_coords = [y for x, y in coords]
    
    # Create the plot
    plt.figure(figsize=(16, 10))
    
    # Plot as connected lines
    plt.plot(x_coords, y_coords, linewidth=0.5, alpha=0.7, color='red')
    
    # Invert y-axis since screen coordinates have origin at top-left
    plt.gca().invert_yaxis()
    
    plt.title("Mouse Movement Trail (Connected)")
    plt.xlabel("X coordinate")
    plt.ylabel("Y coordinate")
    plt.grid(True, alpha=0.3)
    
    # Save the plot
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"Connected plot saved as {save_path}")

def main():
    filename = "uploads?key=b2ea0c5389ebbfbeddc3c2a95526c0e5049a0e7a736f7dea092c6234bfc29e2d%2Fmouse_movements.txt"
    
    print("Parsing mouse movement coordinates...")
    coords = parse_coordinates(filename)
    
    print("\nAnalyzing coordinates...")
    analyze_coordinates(coords)
    
    print("\nCreating visualizations...")
    plot_mouse_trail(coords)
    plot_connected_trail(coords)

if __name__ == "__main__":
    main()
