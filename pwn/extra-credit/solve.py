#!/usr/bin/env python3
"""
TJCTF Extra Credit - Password Extractor via Timing Attack
"""
import subprocess
import time

def test_password(password):
    """Test password and measure timing"""
    start = time.time()
    subprocess.run(['./gradeViewer'], input=f"-62482\n{password}\n",
                  text=True, capture_output=True, timeout=3)
    return time.time() - start

def timing_attack():
    """Extract password using timing attack"""
    charset = "abcdefghijklmnopqrstuvwxyz0123456789"
    password = ""

    for _ in range(10):  # Max 10 chars
        best_char, best_time = None, 0

        for char in charset:
            test_pass = password + char
            avg_time = sum(test_password(test_pass) for _ in range(2)) / 2

            if avg_time > best_time:
                best_time = avg_time
                best_char = char

        if best_char:
            password += best_char
            print(f"Found: {password}")

            # Check if complete
            result = subprocess.run(['./gradeViewer'],
                                  input=f"-62482\n{password}\n",
                                  text=True, capture_output=True)
            if "Access granted" in result.stdout:
                return password
        else:
            break

    return password

if __name__ == "__main__":
    print("Extracting password...")
    password = timing_attack()
    print(f"\nPassword: {password}")
