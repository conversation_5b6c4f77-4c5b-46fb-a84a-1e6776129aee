#!/usr/bin/env python3

from pwn import *
import struct

# Let's create a simple test to see what's happening
p = process('./chall')

print("=== Testing basic flow ===")

# Receive initial prompt
p.recvuntil(b"Enter the name of your building: ")

# Send a simple name
p.sendline(b"TestBuilding")

# Enter building size (must be < 10 to pass approval)
p.recvuntil(b"Enter the size of your building (in acres): ")
p.sendline(b"5")

# Enter coordinates (must be < 200 to pass approval)
p.recvuntil(b"Enter the east-west coordinate or your building (miles east of the city center): ")
p.sendline(b"100")

p.recvuntil(b"Enter the north-south coordinate or your building (miles north of the city center): ")
p.sendline(b"100")

# Should get approval
try:
    response = p.recvuntil(b"Enter the east-west coordinate: ")
    print("SUCCESS: Got to coordinate guessing phase")
    print("Response:", response)
    
    # Try some random guesses to see what happens
    p.sendline(b"123")
    
    response = p.recvuntil(b"Enter the north-south coordinate: ")
    print("Got second prompt")
    
    p.sendline(b"456")
    
    # Check response
    response = p.recvall(timeout=2)
    print("Final response:", response)
    
except Exception as e:
    print(f"Error: {e}")
    print("Received so far:")
    print(p.recvall())

p.close()
