#!/usr/bin/env python3

from pwn import *

# Let me try a very simple approach - maybe there's a race condition or 
# the random values are predictable

def test_simple_values():
    p = remote('tjc.tf', 31489)
    
    try:
        p.recvuntil(b"Enter the name of your building: ")
        p.sendline(b"test")
        
        p.recvuntil(b"Enter the size of your building (in acres): ")
        p.sendline(b"5")
        
        p.recvuntil(b"Enter the east-west coordinate or your building (miles east of the city center): ")
        p.sendline(b"100")
        
        p.recvuntil(b"Enter the north-south coordinate or your building (miles north of the city center): ")
        p.sendline(b"100")
        
        p.recvuntil(b"Enter the east-west coordinate: ")
        
        # Try some common values that might be used in CTFs
        common_values = [0, 1, 42, 100, 1337, 31337, 65536, -1]
        
        for x in common_values:
            for y in common_values:
                p2 = remote('tjc.tf', 31489)
                try:
                    p2.recvuntil(b"Enter the name of your building: ")
                    p2.sendline(b"test")
                    
                    p2.recvuntil(b"Enter the size of your building (in acres): ")
                    p2.sendline(b"5")
                    
                    p2.recvuntil(b"Enter the east-west coordinate or your building (miles east of the city center): ")
                    p2.sendline(b"100")
                    
                    p2.recvuntil(b"Enter the north-south coordinate or your building (miles north of the city center): ")
                    p2.sendline(b"100")
                    
                    p2.recvuntil(b"Enter the east-west coordinate: ")
                    p2.sendline(str(x).encode())
                    
                    p2.recvuntil(b"Enter the north-south coordinate: ")
                    p2.sendline(str(y).encode())
                    
                    response = p2.recvall(timeout=2)
                    if b"Correct!" in response:
                        print(f"SUCCESS: x={x}, y={y}")
                        print(response)
                        return True
                        
                except:
                    pass
                finally:
                    p2.close()
        
        return False
        
    finally:
        p.close()

# Maybe the values are constant or there's some other pattern
print("Testing common values...")
if test_simple_values():
    exit(0)

# Let me try a different approach - maybe the coordinates are related to the building coordinates
def test_building_coordinates():
    p = remote('tjc.tf', 31489)
    
    try:
        p.recvuntil(b"Enter the name of your building: ")
        p.sendline(b"test")
        
        p.recvuntil(b"Enter the size of your building (in acres): ")
        p.sendline(b"5")
        
        # Try different building coordinates and see if they affect the HQ coordinates
        for bx in [50, 100, 150]:
            for by in [50, 100, 150]:
                p2 = remote('tjc.tf', 31489)
                try:
                    p2.recvuntil(b"Enter the name of your building: ")
                    p2.sendline(b"test")
                    
                    p2.recvuntil(b"Enter the size of your building (in acres): ")
                    p2.sendline(b"5")
                    
                    p2.recvuntil(b"Enter the east-west coordinate or your building (miles east of the city center): ")
                    p2.sendline(str(bx).encode())
                    
                    p2.recvuntil(b"Enter the north-south coordinate or your building (miles north of the city center): ")
                    p2.sendline(str(by).encode())
                    
                    p2.recvuntil(b"Enter the east-west coordinate: ")
                    
                    # Try guessing the same coordinates as the building
                    p2.sendline(str(bx).encode())
                    
                    p2.recvuntil(b"Enter the north-south coordinate: ")
                    p2.sendline(str(by).encode())
                    
                    response = p2.recvall(timeout=2)
                    if b"Correct!" in response:
                        print(f"SUCCESS with building coordinates: bx={bx}, by={by}")
                        print(response)
                        return True
                        
                except:
                    pass
                finally:
                    p2.close()
        
        return False
        
    finally:
        p.close()

print("Testing if HQ coordinates match building coordinates...")
if test_building_coordinates():
    exit(0)

print("Simple approaches didn't work. The vulnerability might be more complex.")
