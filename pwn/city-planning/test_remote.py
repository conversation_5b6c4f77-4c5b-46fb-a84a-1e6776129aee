#!/usr/bin/env python3

from pwn import *
import struct

# Test the remote server
p = remote('tjc.tf', 31489)

print("=== Testing remote server ===")

# Receive initial prompt
p.recvuntil(b"Enter the name of your building: ")

# Send a simple name
p.sendline(b"TestBuilding")

# Enter building size (must be < 10 to pass approval)
p.recvuntil(b"Enter the size of your building (in acres): ")
p.sendline(b"5")

# Enter coordinates (must be < 200 to pass approval)
p.recvuntil(b"Enter the east-west coordinate or your building (miles east of the city center): ")
p.sendline(b"100")

p.recvuntil(b"Enter the north-south coordinate or your building (miles north of the city center): ")
p.sendline(b"100")

# Should get approval
try:
    response = p.recvuntil(b"Enter the east-west coordinate: ")
    print("SUCCESS: Got to coordinate guessing phase")
    
    # Try some random guesses to see what happens
    p.sendline(b"123")
    
    response = p.recvuntil(b"Enter the north-south coordinate: ")
    print("Got second prompt")
    
    p.sendline(b"456")
    
    # Check response
    response = p.recvall(timeout=2)
    print("Final response:", response)
    
except Exception as e:
    print(f"Error: {e}")
    print("Received so far:")
    print(p.recvall())

p.close()

# Now let's try a different approach - maybe the vulnerability is simpler
# Let me try to see if there's a buffer overflow or format string vulnerability

print("\n=== Testing for other vulnerabilities ===")

# Test for buffer overflow in name
p = remote('tjc.tf', 31489)

try:
    p.recvuntil(b"Enter the name of your building: ")
    # Try a long name
    p.sendline(b"A" * 100)
    
    p.recvuntil(b"Enter the size of your building (in acres): ")
    p.sendline(b"5")
    
    p.recvuntil(b"Enter the east-west coordinate or your building (miles east of the city center): ")
    p.sendline(b"100")
    
    p.recvuntil(b"Enter the north-south coordinate or your building (miles north of the city center): ")
    p.sendline(b"100")
    
    response = p.recvall(timeout=2)
    print("Buffer overflow test response:", response)
    
except Exception as e:
    print(f"Buffer overflow test error: {e}")

p.close()

# Test for format string vulnerability
p = remote('tjc.tf', 31489)

try:
    p.recvuntil(b"Enter the name of your building: ")
    # Try format string
    p.sendline(b"%x %x %x %x")
    
    p.recvuntil(b"Enter the size of your building (in acres): ")
    p.sendline(b"5")
    
    p.recvuntil(b"Enter the east-west coordinate or your building (miles east of the city center): ")
    p.sendline(b"100")
    
    p.recvuntil(b"Enter the north-south coordinate or your building (miles north of the city center): ")
    p.sendline(b"100")
    
    response = p.recvall(timeout=2)
    print("Format string test response:", response)
    
except Exception as e:
    print(f"Format string test error: {e}")

p.close()
