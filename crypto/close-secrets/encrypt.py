import random
from random import randint
import sys
from Crypto.Util import number
import hashlib 

def encrypt_outer(plaintext_ords, key):
    cipher = []
    key_offset = key % 256
    for val in plaintext_ords:
        if not isinstance(val, int):
            raise TypeError
        cipher.append((val + key_offset) * key)
    return cipher

def dynamic_xor_encrypt(plaintext_bytes, text_key_bytes):
    encrypted_ords = []
    key_length = len(text_key_bytes)
    if not isinstance(plaintext_bytes, bytes):
        raise TypeError
    for i, byte_val in enumerate(plaintext_bytes[::-1]):
        key_byte = text_key_bytes[i % key_length]
        encrypted_ords.append(byte_val ^ key_byte)
    return encrypted_ords

def generate_dh_key():
    p = number.getPrime(1024)
    g = number.getPrime(1024)
    a = randint(p - 10, p)
    b = randint(g - 10, g)
    u = pow(g, a, p)
    v = pow(g, b, p)
    key = pow(v, a, p)
    b_key = pow(u, b, p)
    if key != b_key:
        sys.exit(1)
    return p, g, u, v, key

def generate_challenge_files(flag_file="flag.txt", params_out="params.txt", enc_flag_out="enc_flag"):
    try:
        with open(flag_file, "r") as f:
            flag_plaintext = f.read().strip()
    except FileNotFoundError:
        sys.exit(1)
    flag_bytes = flag_plaintext.encode('utf-8')
    p, g, u, v, shared_key = generate_dh_key()
    xor_key_str = hashlib.sha256(str(shared_key).encode()).hexdigest()
    xor_key_bytes = xor_key_str.encode('utf-8')
    intermediate_ords = dynamic_xor_encrypt(flag_bytes, xor_key_bytes)
    final_cipher = encrypt_outer(intermediate_ords, shared_key)
    with open(params_out, "w") as f:
        f.write(f"p = {p}\n")
        f.write(f"g = {g}\n")
        f.write(f"u = {u}\n")
        f.write(f"v = {v}\n")
    with open(enc_flag_out, "w") as f:
        f.write(str(final_cipher))

def decrypt_outer(cipher_ords, key):
    plaintext = []
    key_offset = key % 256
    for val in cipher_ords:
        # Reverse the operation: (val + key_offset) * key
        # So: val = (original + key_offset) * key
        # Therefore: original = (val / key) - key_offset
        original = (val // key) - key_offset
        plaintext.append(original)
    return plaintext

def dynamic_xor_decrypt(encrypted_ords, text_key_bytes):
    decrypted_bytes = []
    key_length = len(text_key_bytes)
    for i, encrypted_ord in enumerate(encrypted_ords):
        key_byte = text_key_bytes[i % key_length]
        decrypted_bytes.append(encrypted_ord ^ key_byte)
    # Reverse the bytes since they were reversed during encryption
    return bytes(decrypted_bytes[::-1])

def solve_challenge():
    # Read parameters
    with open("params.txt", "r") as f:
        lines = f.readlines()

    p = int(lines[0].split(" = ")[1].strip())
    g = int(lines[1].split(" = ")[1].strip())
    u = int(lines[2].split(" = ")[1].strip())
    v = int(lines[3].split(" = ")[1].strip())

    # Read encrypted flag
    with open("enc_flag", "r") as f:
        enc_flag_str = f.read().strip()

    # Parse the encrypted flag list
    final_cipher = eval(enc_flag_str)

    print(f"Trying to crack with p={p}")
    print(f"g={g}")
    print(f"u={u}")
    print(f"v={v}")

    # Try all possible values of a and b
    for a in range(p - 10, p + 1):
        for b in range(g - 10, g + 1):
            try:
                # Calculate what u and v should be with these a, b values
                expected_u = pow(g, a, p)
                expected_v = pow(g, b, p)

                # Check if this matches the given u, v
                if expected_u == u and expected_v == v:
                    print(f"Found matching a={a}, b={b}")

                    # Calculate shared key
                    shared_key = pow(v, a, p)

                    # Verify with the other calculation
                    shared_key_verify = pow(u, b, p)
                    if shared_key != shared_key_verify:
                        print("Key mismatch!")
                        continue

                    print(f"Shared key: {shared_key}")

                    # Generate XOR key
                    xor_key_str = hashlib.sha256(str(shared_key).encode()).hexdigest()
                    xor_key_bytes = xor_key_str.encode('utf-8')

                    # Decrypt
                    try:
                        intermediate_ords = decrypt_outer(final_cipher, shared_key)
                        flag_bytes = dynamic_xor_decrypt(intermediate_ords, xor_key_bytes)
                        flag = flag_bytes.decode('utf-8')
                        print(f"Decrypted flag: {flag}")
                        return flag
                    except Exception as e:
                        print(f"Decryption failed: {e}")
                        continue

            except Exception as e:
                continue

    print("No valid solution found")
    return None

if __name__ == "__main__":
    try:
        with open("flag.txt", "x") as f:
            f.write("tjctf{d3f4ult_fl4g_f0r_t3st1ng}")
    except FileExistsError:
        pass

    # Uncomment to generate challenge files
    # generate_challenge_files()

    # Solve the challenge
    solve_challenge()
