#!/usr/bin/env python3

import hashlib

def decrypt_outer(cipher_ords, key):
    plaintext = []
    key_offset = key % 256
    for val in cipher_ords:
        # Reverse the operation: (val + key_offset) * key
        # So: val = (original + key_offset) * key
        # Therefore: original = (val / key) - key_offset
        original = (val // key) - key_offset
        plaintext.append(original)
    return plaintext

def dynamic_xor_decrypt(encrypted_ords, text_key_bytes):
    decrypted_bytes = []
    key_length = len(text_key_bytes)
    for i, encrypted_ord in enumerate(encrypted_ords):
        key_byte = text_key_bytes[i % key_length]
        decrypted_bytes.append(encrypted_ord ^ key_byte)
    # Reverse the bytes since they were reversed during encryption
    return bytes(decrypted_bytes[::-1])

def solve_challenge():
    # Read parameters
    with open("params.txt", "r") as f:
        lines = f.readlines()
    
    p = int(lines[0].split(" = ")[1].strip())
    g = int(lines[1].split(" = ")[1].strip())
    u = int(lines[2].split(" = ")[1].strip())
    v = int(lines[3].split(" = ")[1].strip())
    
    # Read encrypted flag
    with open("enc_flag", "r") as f:
        enc_flag_str = f.read().strip()
    
    # Parse the encrypted flag list
    final_cipher = eval(enc_flag_str)
    
    print(f"Trying to crack...")
    print(f"p has {len(str(p))} digits")
    print(f"g has {len(str(g))} digits")
    
    # Try all possible values of a and b
    # a is in range [p-10, p-1] (10 values)
    # b is in range [g-10, g-1] (10 values)
    
    for a in range(p - 10, p):
        for b in range(g - 10, g):
            try:
                # Calculate what u and v should be with these a, b values
                expected_u = pow(g, a, p)
                expected_v = pow(g, b, p)
                
                # Check if this matches the given u, v
                if expected_u == u and expected_v == v:
                    print(f"Found matching a={a-p+10}, b={b-g+10} (relative to max)")
                    
                    # Calculate shared key
                    shared_key = pow(v, a, p)
                    
                    # Verify with the other calculation
                    shared_key_verify = pow(u, b, p)
                    if shared_key != shared_key_verify:
                        print("Key mismatch!")
                        continue
                    
                    print(f"Shared key found!")
                    
                    # Generate XOR key
                    xor_key_str = hashlib.sha256(str(shared_key).encode()).hexdigest()
                    xor_key_bytes = xor_key_str.encode('utf-8')
                    
                    # Decrypt
                    try:
                        intermediate_ords = decrypt_outer(final_cipher, shared_key)
                        flag_bytes = dynamic_xor_decrypt(intermediate_ords, xor_key_bytes)
                        flag = flag_bytes.decode('utf-8')
                        print(f"Decrypted flag: {flag}")
                        return flag
                    except Exception as e:
                        print(f"Decryption failed: {e}")
                        continue
                        
            except Exception as e:
                continue
    
    print("No valid solution found")
    return None

if __name__ == "__main__":
    solve_challenge()
