# From the encryption code, j and i both map to '01000', v and u both map to '10011'
baconian = {
'a': '00000',	'b': '00001',
'c': '00010',	'd': '00011',
'e': '00100',	'f': '00101',
'g': '00110',	'h': '00111',
'i': '01000',    'j': '01000',
'k': '01001',    'l': '01010',
'm': '01011',    'n': '01100',
'o': '01101',    'p': '01110',
'q': '01111',    'r': '10000',
's': '10001',    't': '10010',
'u': '10011',    'v': '10011',
'w': '10100',	'x': '10101',
'y': '10110',	'z': '10111'}

# Reverse mapping - for ambiguous cases, we'll need context
reverse_baconian = {
    '00000': 'a', '00001': 'b', '00010': 'c', '00011': 'd',
    '00100': 'e', '00101': 'f', '00110': 'g', '00111': 'h',
    '01000': 'j',  # Could be i or j, let's try j first since flag likely starts with tjctf
    '01001': 'k', '01010': 'l', '01011': 'm',
    '01100': 'n', '01101': 'o', '01110': 'p', '01111': 'q',
    '10000': 'r', '10001': 's', '10010': 't', '10011': 'u',  # Could be u or v
    '10100': 'w', '10101': 'x', '10110': 'y', '10111': 'z'
}

# Read out.txt and shift +13
with open('out.txt') as f:
    shifted = ''.join([chr(ord(c) + 13) for c in f.read().strip()])

print(f"Shifted text: {shifted}")
print(f"Length: {len(shifted)}")

# Process in chunks of 5
flag = ''
for i in range(0, len(shifted), 5):
    chunk = shifted[i:i+5]
    if len(chunk) < 5:
        break
    bits = ''.join(['1' if c.isupper() else '0' for c in chunk])

    # Handle ambiguous cases
    if bits == '01000':  # Could be i or j
        # For flag format, after tjctf{ we expect more normal letters
        if i == 5:  # Second character should be 'j' for tjctf
            letter = 'j'
        elif len(flag) > 5:  # After tjctf{, prefer 'i'
            letter = 'i'
        else:
            letter = 'j'  # Default to j for early positions
    elif bits == '10011':  # Could be u or v
        letter = 'u'  # Default to u
    else:
        letter = reverse_baconian.get(bits, '?')

    print(f"Chunk: {chunk} -> Bits: {bits} -> Letter: {letter}")
    flag += letter

print('Recovered flag:', flag)

# Let's also try all i's for the ambiguous case
print("\nTrying with all i's for 01000:")
flag2 = ''
for i in range(0, len(shifted), 5):
    chunk = shifted[i:i+5]
    if len(chunk) < 5:
        break
    bits = ''.join(['1' if c.isupper() else '0' for c in chunk])

    if bits == '01000':
        letter = 'i'
    elif bits == '10011':
        letter = 'u'
    else:
        letter = reverse_baconian.get(bits, '?')

    flag2 += letter

print('Flag with i:', flag2)

# The pattern suggests "oink" is repeated. Let me try different combinations
print("\nLet's look for patterns:")
print("Current flag:", flag)
print("Looking for 'oink' pattern...")

# Try mixed approach - j for first occurrence, then i
print("\nTrying mixed approach:")
flag3 = ''
for i in range(0, len(shifted), 5):
    chunk = shifted[i:i+5]
    if len(chunk) < 5:
        break
    bits = ''.join(['1' if c.isupper() else '0' for c in chunk])

    if bits == '01000':
        # First few should be j for tjctf, then switch to i
        if i <= 5:  # First two characters
            letter = 'j' if i == 5 else 'i'  # Second char is j
        else:
            letter = 'i'
    elif bits == '10011':
        letter = 'u'
    else:
        letter = reverse_baconian.get(bits, '?')

    flag3 += letter

print('Mixed flag:', flag3)

# The flag might be tjctf{oink} repeated or similar
# Let's see if we can find where the actual flag content starts
print(f"\nFlag analysis:")
print(f"Length: {len(flag)}")
if len(flag) >= 5:
    print(f"First 5 chars: {flag[:5]}")
if len(flag) >= 10:
    print(f"Next 5 chars: {flag[5:10]}")
if len(flag) >= 15:
    print(f"Next 5 chars: {flag[10:15]}")

# Try to construct the final flag
if flag.startswith('tjctf'):
    # Remove tjctf and look at the rest
    content = flag[5:]
    print(f"Content after tjctf: {content}")

    # Look for repeating patterns
    if 'oink' in content:
        print("Found 'oink' pattern!")
        # The flag might be tjctf{oink} or similar
        final_flag = f"tjctf{{{content[:4]}}}"  # Take first 4 chars after tjctf
        print(f"Possible final flag: {final_flag}")

        # Or maybe it's longer
        if len(content) >= 8:
            final_flag2 = f"tjctf{{{content[:8]}}}"
            print(f"Alternative flag: {final_flag2}")

        # Or maybe the whole content
        final_flag3 = f"tjctf{{{content}}}"
        print(f"Full content flag: {final_flag3}")
