#!/usr/bin/env python3

from Crypto.Util.number import long_to_bytes
import re
import sys

def extended_gcd(a, b):
    """Extended Euclidean Algorithm"""
    if a == 0:
        return b, 0, 1
    gcd, x1, y1 = extended_gcd(b % a, a)
    x = y1 - (b // a) * x1
    y = x1
    return gcd, x, y

def chinese_remainder_theorem(remainders, moduli):
    """
    Solve system of congruences using Chinese Remainder Theorem
    x ≡ remainders[i] (mod moduli[i])
    """
    total = 0
    prod = 1
    for m in moduli:
        prod *= m
    
    for r, m in zip(remainders, moduli):
        p = prod // m
        _, inv, _ = extended_gcd(p, m)
        total += r * inv * p
    
    return total % prod

def nth_root(x, n):
    """Calculate the nth root of x using integer arithmetic"""
    if x == 0:
        return 0
    if x == 1:
        return 1

    # Use <PERSON>'s method with integer arithmetic
    # Start with a reasonable guess
    bit_length = x.bit_length()
    guess = 1 << ((bit_length + n - 1) // n)

    # Newton's method: x_{k+1} = ((n-1)*x_k + x/x_k^(n-1)) / n
    prev_guess = 0
    while guess != prev_guess:
        prev_guess = guess
        try:
            guess = ((n - 1) * guess + x // (guess ** (n - 1))) // n
        except:
            # If we get overflow or other issues, fall back to binary search
            break

    # Verify and adjust if needed
    while guess ** n > x:
        guess -= 1
    while (guess + 1) ** n <= x:
        guess += 1

    return guess

def solve_rsa_crt():
    """Solve the RSA CRT attack"""
    
    # Parse the output.txt file
    with open('output.txt', 'r') as f:
        content = f.read()
    
    # Extract e
    e_match = re.search(r'e = (\d+)', content)
    e = int(e_match.group(1))
    print(f"Found e = {e}")
    
    # Extract all n and c values
    n_values = []
    c_values = []
    
    for i in range(e):
        n_match = re.search(rf'n{i} = (\d+)', content)
        c_match = re.search(rf'c{i} = (\d+)', content)
        
        if n_match and c_match:
            n_values.append(int(n_match.group(1)))
            c_values.append(int(c_match.group(1)))
        else:
            print(f"Could not find n{i} or c{i}")
            break
    
    print(f"Found {len(n_values)} moduli and {len(c_values)} ciphertexts")
    
    # Check that all moduli are pairwise coprime (they should be since they're products of different primes)
    print("Checking if moduli are pairwise coprime...")
    for i in range(len(n_values)):
        for j in range(i + 1, len(n_values)):
            gcd, _, _ = extended_gcd(n_values[i], n_values[j])
            if gcd != 1:
                print(f"WARNING: gcd(n{i}, n{j}) = {gcd} != 1")
    
    print("All moduli appear to be pairwise coprime. Proceeding with CRT...")
    
    # Apply Chinese Remainder Theorem
    # We have: m^e ≡ c_i (mod n_i) for all i
    # CRT gives us: m^e ≡ result (mod N) where N = product of all n_i
    print("Applying Chinese Remainder Theorem...")
    result = chinese_remainder_theorem(c_values, n_values)

    print("CRT completed. Computing e-th root...")

    # Take the e-th root to get m
    m = nth_root(result, e)

    print(f"Computed m = {m}")
    
    # Convert to bytes to get the flag
    try:
        flag = long_to_bytes(m)
        print(f"Flag: {flag}")
        return flag
    except Exception as ex:
        print(f"Error converting to bytes: {ex}")
        return None

if __name__ == "__main__":
    solve_rsa_crt()
