#!/usr/bin/env python3

from Crypto.Util.number import long_to_bytes
import re

def extended_gcd(a, b):
    if a == 0:
        return b, 0, 1
    gcd, x1, y1 = extended_gcd(b % a, a)
    x = y1 - (b // a) * x1
    y = x1
    return gcd, x, y

def chinese_remainder_theorem(remainders, moduli):
    total = 0
    prod = 1
    for m in moduli:
        prod *= m
    
    for r, m in zip(remainders, moduli):
        p = prod // m
        _, inv, _ = extended_gcd(p, m)
        total += r * inv * p
    
    return total % prod

def nth_root(x, n):
    if x <= 1:
        return x
    
    # <PERSON>'s method for nth root
    bit_length = x.bit_length()
    guess = 1 << ((bit_length + n - 1) // n)
    
    prev_guess = 0
    while guess != prev_guess:
        prev_guess = guess
        guess = ((n - 1) * guess + x // (guess ** (n - 1))) // n
    
    # Verify result
    while guess ** n > x:
        guess -= 1
    
    return guess

# Parse output.txt
with open('output.txt', 'r') as f:
    content = f.read()

# Extract e, n values, and c values
e = int(re.search(r'e = (\d+)', content).group(1))
n_values = [int(re.search(rf'n{i} = (\d+)', content).group(1)) for i in range(e)]
c_values = [int(re.search(rf'c{i} = (\d+)', content).group(1)) for i in range(e)]

# Apply CRT to solve m^e ≡ c_i (mod n_i)
m_to_e = chinese_remainder_theorem(c_values, n_values)

# Take eth root to get m
m = nth_root(m_to_e, e)

# Convert to flag
flag = long_to_bytes(m)
print(flag.decode())
