import numpy as np

# Try a more numerically stable approach

# Read data
with open("encoded.txt", "r") as f:
    lines = f.readlines()

encoded = []
for line in lines:
    row = list(map(int, line.strip().split()))
    encoded.append(row)

encoded = np.array(encoded, dtype=np.float64)

# Filler setup
filler = "In cybersecurity, a CTF (Capture The Flag) challenge is a competitive, gamified event where participants, either individually or in teams, are tasked with finding and exploiting vulnerabilities in systems to capture hidden information known as flags. These flags are typically used to score points. CTFs test skills in areas like cryptography, web security, reverse engineering, and forensics, offering an exciting way to learn, practice, and showcase cybersecurity expertise.  This flag is for you: "

filler_bin = "".join([bin(ord(c))[2:].zfill(8) for c in filler])
n = 64
filler_bin += "0" * (n - len(filler_bin) % n)
filler_matrix = np.array([list(map(int, list(filler_bin[i:i+n]))) for i in range(0, len(filler_bin), n)], dtype=np.float64)

A = filler_matrix  # 63 x 64
B = encoded[:63]   # 63 x 64

print(f"A shape: {A.shape}, B shape: {B.shape}")
print(f"A rank: {np.linalg.matrix_rank(A)}")

# Try regularized normal equations
print("Trying regularized normal equations...")

for lambda_reg in [1e-3, 1e-6, 1e-9, 1e-12]:
    print(f"\n--- Lambda = {lambda_reg} ---")
    
    # Regularized normal equations: (A^T A + λI) X = A^T B
    AtA = A.T @ A
    AtB = A.T @ B
    AtA_reg = AtA + lambda_reg * np.eye(64)
    
    try:
        key_matrix = np.linalg.solve(AtA_reg, AtB)
        
        # Check reconstruction
        reconstruction = A @ key_matrix
        error = np.linalg.norm(reconstruction - B, 'fro')
        print(f"Reconstruction error: {error}")
        
        # Check condition number
        cond_num = np.linalg.cond(key_matrix)
        print(f"Key matrix condition number: {cond_num:.2e}")
        
        # Try to decode
        try:
            if cond_num < 1e12:  # Only try if reasonably conditioned
                key_inv = np.linalg.inv(key_matrix)
                decoded = encoded @ key_inv
                
                # Convert to binary
                decoded_binary = np.round(decoded).astype(int)
                decoded_binary = np.clip(decoded_binary, 0, 1)
                
                # Convert to text
                text = ""
                for row in decoded_binary:
                    for i in range(0, 64, 8):
                        byte_bits = row[i:i+8]
                        byte_str = "".join(map(str, byte_bits))
                        if len(byte_str) == 8:
                            try:
                                char_val = int(byte_str, 2)
                                if 32 <= char_val <= 126:
                                    text += chr(char_val)
                                else:
                                    text += f"[{char_val}]"
                            except:
                                text += "?"
                
                # Check if this looks good
                if text.startswith("In cybersecurity"):
                    print("✓ Good filler text!")
                    
                    if "This flag is for you:" in text:
                        marker_pos = text.find("This flag is for you:")
                        after_marker = text[marker_pos + len("This flag is for you:"):]
                        print(f"After marker: '{after_marker[:50]}'")
                        
                        if "tjctf{" in after_marker:
                            start = after_marker.find("tjctf{")
                            end = after_marker.find("}", start) + 1
                            if end > start:
                                flag = after_marker[start:end]
                                print(f"*** FLAG: {flag} ***")
                else:
                    print(f"✗ Bad filler: '{text[:30]}'")
            else:
                print("Key matrix too ill-conditioned, using pseudoinverse")
                key_inv = np.linalg.pinv(key_matrix)
                decoded = encoded @ key_inv
                
                # Try different thresholds
                for thresh in [0.3, 0.5, 0.7]:
                    decoded_binary = (decoded > thresh).astype(int)
                    
                    text = ""
                    for row in decoded_binary:
                        for i in range(0, 64, 8):
                            byte_bits = row[i:i+8]
                            byte_str = "".join(map(str, byte_bits))
                            if len(byte_str) == 8:
                                try:
                                    char_val = int(byte_str, 2)
                                    if 32 <= char_val <= 126:
                                        text += chr(char_val)
                                    else:
                                        text += "?"
                                except:
                                    text += "?"
                    
                    if text.startswith("In cybersecurity"):
                        print(f"✓ Threshold {thresh} works!")

                        # Show the end of the text where flag should be
                        if "This flag is for you:" in text:
                            marker_pos = text.find("This flag is for you:")
                            after_marker = text[marker_pos + len("This flag is for you:"):]
                            print(f"After marker: '{after_marker[:50]}'")

                            if "tjctf{" in after_marker:
                                start = after_marker.find("tjctf{")
                                end = after_marker.find("}", start) + 1
                                if end > start:
                                    flag = after_marker[start:end]
                                    print(f"*** FLAG: {flag} ***")
                                    break
                            else:
                                print("No tjctf{ found after marker")
                        else:
                            print("No flag marker found")
                            print(f"End of text: '{text[-50:]}'")
                        break
                
        except np.linalg.LinAlgError as e:
            print(f"Matrix inversion failed: {e}")
            
    except np.linalg.LinAlgError as e:
        print(f"Regularized solve failed: {e}")
