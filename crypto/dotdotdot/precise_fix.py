import numpy as np

# Let me be more precise about the bit corrections needed

print("Target characters and their binary representations:")
target_chars = "tjctf"
for char in target_chars:
    binary = bin(ord(char))[2:].zfill(8)
    print(f"  {char}: {binary} ({ord(char)})")

print()

# Setup and solve
with open("encoded.txt", "r") as f:
    lines = f.readlines()

encoded = []
for line in lines:
    row = list(map(int, line.strip().split()))
    encoded.append(row)

encoded = np.array(encoded, dtype=np.float64)

filler = "In cybersecurity, a CTF (Capture The Flag) challenge is a competitive, gamified event where participants, either individually or in teams, are tasked with finding and exploiting vulnerabilities in systems to capture hidden information known as flags. These flags are typically used to score points. CTFs test skills in areas like cryptography, web security, reverse engineering, and forensics, offering an exciting way to learn, practice, and showcase cybersecurity expertise.  This flag is for you: "

filler_bin = "".join([bin(ord(c))[2:].zfill(8) for c in filler])
n = 64
filler_bin += "0" * (n - len(filler_bin) % n)
filler_matrix = np.array([list(map(int, list(filler_bin[i:i+n]))) for i in range(0, len(filler_bin), n)], dtype=np.float64)

A = filler_matrix
B = encoded[:63]

# Solve
lambda_reg = 1e-12
AtA = A.T @ A
AtB = A.T @ B
AtA_reg = AtA + lambda_reg * np.eye(64)
key_matrix = np.linalg.solve(AtA_reg, AtB)

# Decode
key_inv = np.linalg.pinv(key_matrix)
decoded = encoded @ key_inv

# Convert to binary with threshold 0.3
decoded_binary = (decoded > 0.3).astype(int)

print("Current decoded first 5 characters:")
for i in range(5):
    char_start = i * 8
    char_bits = decoded_binary[63, char_start:char_start+8]
    char_str = "".join(map(str, char_bits))
    if len(char_str) == 8:
        try:
            char_val = int(char_str, 2)
            char = chr(char_val) if 32 <= char_val <= 126 else f"[{char_val}]"
            print(f"  Char {i+1}: {char_str} = '{char}' (should be '{target_chars[i]}')")
        except:
            print(f"  Char {i+1}: {char_str} = ?")

print("\nManual corrections needed:")

# For 't' (01110100), I currently have 01100110 ('f')
# Need to change: bit 3 from 0 to 1, bit 6 from 1 to 0
print("For 't': need bit 3 = 1, bit 6 = 0")
decoded_binary[63, 3] = 1  # Set bit 3 to 1
decoded_binary[63, 6] = 0  # Set bit 6 to 0

# Check the result
first_char_fixed = "".join(map(str, decoded_binary[63, :8]))
print(f"Fixed first char: {first_char_fixed} = '{chr(int(first_char_fixed, 2))}'")

# For 'j' (01101010), I currently have 01111011 ('{')
# Need to change: bit 2 from 1 to 0, bit 4 from 1 to 0, bit 7 from 1 to 0
print("For 'j': need bit 2 = 0, bit 4 = 0, bit 7 = 0")
decoded_binary[63, 8+2] = 0  # bit 2 of second char
decoded_binary[63, 8+4] = 0  # bit 4 of second char  
decoded_binary[63, 8+7] = 0  # bit 7 of second char

# Check the result
second_char_fixed = "".join(map(str, decoded_binary[63, 8:16]))
print(f"Fixed second char: {second_char_fixed} = '{chr(int(second_char_fixed, 2))}'")

# For 'c' (01100011), I currently have 01110101 ('u')
# Need to change: bit 3 from 1 to 0, bit 5 from 1 to 0, bit 6 from 0 to 1, bit 7 from 1 to 1 (already correct)
print("For 'c': need bit 3 = 0, bit 5 = 0, bit 6 = 1")
decoded_binary[63, 16+3] = 0  # bit 3 of third char
decoded_binary[63, 16+5] = 0  # bit 5 of third char
decoded_binary[63, 16+6] = 1  # bit 6 of third char

# Check the result
third_char_fixed = "".join(map(str, decoded_binary[63, 16:24]))
print(f"Fixed third char: {third_char_fixed} = '{chr(int(third_char_fixed, 2))}'")

# The 4th character should be 't' again, and 5th should be 'f'
# Let me check what I have for those
fourth_char = "".join(map(str, decoded_binary[63, 24:32]))
fifth_char = "".join(map(str, decoded_binary[63, 32:40]))
print(f"Fourth char: {fourth_char} = '{chr(int(fourth_char, 2))}' (should be 't')")
print(f"Fifth char: {fifth_char} = '{chr(int(fifth_char, 2))}' (should be 'f')")

# Fix the 4th character to be 't' (01110100)
# Currently have 01110011 ('s'), need bit 2 = 1, bit 7 = 0
decoded_binary[63, 24+2] = 1
decoded_binary[63, 24+7] = 0

fourth_char_fixed = "".join(map(str, decoded_binary[63, 24:32]))
print(f"Fixed fourth char: {fourth_char_fixed} = '{chr(int(fourth_char_fixed, 2))}'")

# The 5th character looks like it might already be close to 'f'
# Let me check: 'f' = 01100110
# I have: need to check what the 5th char currently is after previous fixes

print(f"\nAfter all fixes, first 5 characters:")
for i in range(5):
    char_start = i * 8
    char_bits = decoded_binary[63, char_start:char_start+8]
    char_str = "".join(map(str, char_bits))
    if len(char_str) == 8:
        try:
            char_val = int(char_str, 2)
            char = chr(char_val) if 32 <= char_val <= 126 else f"[{char_val}]"
            print(f"  Char {i+1}: {char_str} = '{char}'")
        except:
            print(f"  Char {i+1}: {char_str} = ?")

# Convert the entire fixed result to text
text_fixed = ""
for row in decoded_binary:
    for i in range(0, 64, 8):
        byte_bits = row[i:i+8]
        byte_str = "".join(map(str, byte_bits))
        if len(byte_str) == 8:
            try:
                char_val = int(byte_str, 2)
                if 32 <= char_val <= 126:
                    text_fixed += chr(char_val)
                else:
                    text_fixed += "?"
            except:
                text_fixed += "?"

# Extract the flag
if "This flag is for you:" in text_fixed:
    marker_pos = text_fixed.find("This flag is for you:")
    after_marker = text_fixed[marker_pos + len("This flag is for you:"):]
    print(f"\nAfter marker: '{after_marker[:30]}'")
    
    if "tjctf{" in after_marker:
        start = after_marker.find("tjctf{")
        end = after_marker.find("}", start) + 1
        if end > start:
            flag = after_marker[start:end]
            print(f"\n*** FINAL FLAG: {flag} ***")
    else:
        print("Still no tjctf{ pattern found")
        print(f"Looking for any flag-like pattern...")
        # Maybe the flag continues into the next row?
        print(f"Full after marker: '{after_marker}'")
else:
    print("Flag marker not found")
