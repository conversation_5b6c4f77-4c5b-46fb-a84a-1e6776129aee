import numpy as np

# Targeted approach to fix the specific corrupted bits

# Setup and solve
with open("encoded.txt", "r") as f:
    lines = f.readlines()

encoded = []
for line in lines:
    row = list(map(int, line.strip().split()))
    encoded.append(row)

encoded = np.array(encoded, dtype=np.float64)

filler = "In cybersecurity, a CTF (Capture The Flag) challenge is a competitive, gamified event where participants, either individually or in teams, are tasked with finding and exploiting vulnerabilities in systems to capture hidden information known as flags. These flags are typically used to score points. CTFs test skills in areas like cryptography, web security, reverse engineering, and forensics, offering an exciting way to learn, practice, and showcase cybersecurity expertise.  This flag is for you: "

filler_bin = "".join([bin(ord(c))[2:].zfill(8) for c in filler])
n = 64
filler_bin += "0" * (n - len(filler_bin) % n)
filler_matrix = np.array([list(map(int, list(filler_bin[i:i+n]))) for i in range(0, len(filler_bin), n)], dtype=np.float64)

A = filler_matrix
B = encoded[:63]

# Solve
lambda_reg = 1e-12
AtA = A.T @ A
AtB = A.T @ B
AtA_reg = AtA + lambda_reg * np.eye(64)
key_matrix = np.linalg.solve(AtA_reg, AtB)

# Decode
key_inv = np.linalg.pinv(key_matrix)
decoded = encoded @ key_inv

# Convert with threshold 0.3
decoded_binary = (decoded > 0.3).astype(int)

print("Current decoded flag area:")
# Extract flag area
text = ""
for row in decoded_binary:
    for i in range(0, 64, 8):
        byte_bits = row[i:i+8]
        byte_str = "".join(map(str, byte_bits))
        if len(byte_str) == 8:
            try:
                char_val = int(byte_str, 2)
                if 32 <= char_val <= 126:
                    text += chr(char_val)
                else:
                    text += "?"
            except:
                text += "?"

if "This flag is for you:" in text:
    marker_pos = text.find("This flag is for you:")
    after_marker = text[marker_pos + len("This flag is for you:"):]
    print(f"Current: '{after_marker[:25]}'")

# Now let me manually fix the known issues
print("\nManual bit correction:")

# I know the pattern should be: tjctf{...}
# Current first char: f (01100110) -> should be t (01110100)
# Difference: bit 2 should be 1 instead of 0

# Let me look at the raw values for the problematic bits
flag_start_row = 63
print(f"Raw values for first few characters:")

for char_idx in range(5):  # First 5 characters
    print(f"\nCharacter {char_idx + 1}:")
    for bit_idx in range(8):
        global_bit_idx = char_idx * 8 + bit_idx
        row_idx = flag_start_row + global_bit_idx // 64
        bit_in_row = global_bit_idx % 64
        
        if row_idx < decoded.shape[0]:
            raw_val = decoded[row_idx, bit_in_row]
            binary_val = 1 if raw_val > 0.3 else 0
            print(f"  Bit {bit_idx}: {raw_val:6.3f} -> {binary_val}")

# Let me try different thresholds for specific problematic bits
print(f"\nTrying targeted threshold adjustments:")

# Make a copy of the decoded matrix for modification
decoded_fixed = decoded.copy()

# For the first character (should be 't' = 01110100)
# Current: f = 01100110
# Need to flip bit 2 from 0 to 1

# The bit is at position: row 63, bit 2
bit_2_value = decoded[63, 2]
print(f"Bit 2 raw value: {bit_2_value}")

# Try different thresholds for this specific bit
for thresh in [0.1, 0.2, 0.25, 0.3, 0.35]:
    if bit_2_value > thresh:
        print(f"  Threshold {thresh}: bit 2 = 1")
    else:
        print(f"  Threshold {thresh}: bit 2 = 0")

# Let me try using a lower threshold for just the problematic bits
decoded_binary_fixed = (decoded > 0.3).astype(int)

# Manually fix bit 2 of the first character if needed
if decoded[63, 2] > 0.25:  # Use a lower threshold for this bit
    decoded_binary_fixed[63, 2] = 1

# Convert to text with the fix
text_fixed = ""
for row in decoded_binary_fixed:
    for i in range(0, 64, 8):
        byte_bits = row[i:i+8]
        byte_str = "".join(map(str, byte_bits))
        if len(byte_str) == 8:
            try:
                char_val = int(byte_str, 2)
                if 32 <= char_val <= 126:
                    text_fixed += chr(char_val)
                else:
                    text_fixed += "?"
            except:
                text_fixed += "?"

print(f"\nAfter manual fix:")
if "This flag is for you:" in text_fixed:
    marker_pos = text_fixed.find("This flag is for you:")
    after_marker = text_fixed[marker_pos + len("This flag is for you:"):]
    print(f"Fixed: '{after_marker[:25]}'")
    
    if "tjctf{" in after_marker:
        start = after_marker.find("tjctf{")
        end = after_marker.find("}", start) + 1
        if end > start:
            flag = after_marker[start:end]
            print(f"\n*** FIXED FLAG: {flag} ***")

# Let me also try a completely different approach - use multiple thresholds
print(f"\nTrying adaptive thresholds:")

# Use different thresholds for different parts
decoded_adaptive = decoded.copy()
for i in range(decoded.shape[0]):
    for j in range(decoded.shape[1]):
        val = decoded[i, j]
        # Use a more sensitive threshold for values close to 0.5
        if abs(val - 0.5) < 0.2:  # Values close to the boundary
            threshold = 0.4
        else:
            threshold = 0.5
        
        decoded_adaptive[i, j] = 1 if val > threshold else 0

# Convert adaptive result to text
text_adaptive = ""
for row in decoded_adaptive.astype(int):
    for i in range(0, 64, 8):
        byte_bits = row[i:i+8]
        byte_str = "".join(map(str, byte_bits))
        if len(byte_str) == 8:
            try:
                char_val = int(byte_str, 2)
                if 32 <= char_val <= 126:
                    text_adaptive += chr(char_val)
                else:
                    text_adaptive += "?"
            except:
                text_adaptive += "?"

print(f"Adaptive threshold result:")
if "This flag is for you:" in text_adaptive:
    marker_pos = text_adaptive.find("This flag is for you:")
    after_marker = text_adaptive[marker_pos + len("This flag is for you:"):]
    print(f"Adaptive: '{after_marker[:25]}'")
    
    if "tjctf{" in after_marker:
        start = after_marker.find("tjctf{")
        end = after_marker.find("}", start) + 1
        if end > start:
            flag = after_marker[start:end]
            print(f"\n*** ADAPTIVE FLAG: {flag} ***")
