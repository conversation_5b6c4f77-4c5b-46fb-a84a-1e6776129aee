import numpy as np

# Properly decode the flag from the challenge files
# No assumptions, just mathematical solution

# Read encoded data
with open("encoded.txt", "r") as f:
    lines = f.readlines()

encoded = []
for line in lines:
    row = list(map(int, line.strip().split()))
    encoded.append(row)

encoded = np.array(encoded, dtype=np.float64)
print(f"Encoded shape: {encoded.shape}")

# Set up filler matrix (known plaintext)
filler = "In cybersecurity, a CTF (Capture The Flag) challenge is a competitive, gamified event where participants, either individually or in teams, are tasked with finding and exploiting vulnerabilities in systems to capture hidden information known as flags. These flags are typically used to score points. CTFs test skills in areas like cryptography, web security, reverse engineering, and forensics, offering an exciting way to learn, practice, and showcase cybersecurity expertise.  This flag is for you: "

# Convert to binary exactly as in encode.py
filler_bin = "".join([bin(ord(c))[2:].zfill(8) for c in filler])
n = 64
filler_bin += "0" * (n - len(filler_bin) % n)
filler_matrix = np.array([list(map(int, list(filler_bin[i:i+n]))) for i in range(0, len(filler_bin), n)], dtype=np.float64)

print(f"Filler matrix shape: {filler_matrix.shape}")
print(f"Known rows: {filler_matrix.shape[0]}, Total rows: {encoded.shape[0]}")

# Solve: filler_matrix @ key = encoded[:63]
A = filler_matrix  # 63 x 64
B = encoded[:63]   # 63 x 64

print(f"Solving system: A({A.shape}) @ key = B({B.shape})")

# Check if we can solve this exactly
rank_A = np.linalg.matrix_rank(A)
print(f"Rank of A: {rank_A}")

if rank_A == 64:
    print("A has full rank - can solve exactly")
    key_matrix = np.linalg.lstsq(A, B, rcond=None)[0]
else:
    print("A does not have full rank - using least squares")
    key_matrix = np.linalg.lstsq(A, B, rcond=None)[0]

print(f"Key matrix shape: {key_matrix.shape}")

# Verify reconstruction
reconstruction = A @ key_matrix
error = np.linalg.norm(reconstruction - B, 'fro')
print(f"Reconstruction error: {error}")

# Now decode all rows
# original_row = encoded_row @ key_matrix^(-1)
print(f"Key matrix condition number: {np.linalg.cond(key_matrix)}")

try:
    key_inv = np.linalg.inv(key_matrix)
    print("Key matrix is invertible")
except np.linalg.LinAlgError:
    print("Key matrix is singular, using pseudoinverse")
    key_inv = np.linalg.pinv(key_matrix)

# Decode all rows
decoded = encoded @ key_inv
print(f"Decoded shape: {decoded.shape}")
print(f"Decoded value range: [{np.min(decoded):.6f}, {np.max(decoded):.6f}]")

# The decoded values should be close to 0 or 1
# Let's see how close they are
distances_to_binary = np.minimum(np.abs(decoded), np.abs(decoded - 1))
print(f"Average distance to nearest binary: {np.mean(distances_to_binary):.6f}")
print(f"Max distance to nearest binary: {np.max(distances_to_binary):.6f}")

# Convert to binary using different strategies
print(f"\nTrying different binary conversion strategies:")

strategies = {
    'round': lambda x: np.round(x).astype(int),
    'threshold_0.5': lambda x: (x > 0.5).astype(int),
    'closest_binary': lambda x: np.round(x).astype(int)
}

for name, strategy in strategies.items():
    print(f"\n--- Strategy: {name} ---")
    
    decoded_binary = strategy(decoded)
    decoded_binary = np.clip(decoded_binary, 0, 1)
    
    # Convert to text
    text = ""
    for row in decoded_binary:
        for i in range(0, 64, 8):
            byte_bits = row[i:i+8]
            byte_str = "".join(map(str, byte_bits))
            if len(byte_str) == 8:
                try:
                    char_val = int(byte_str, 2)
                    if 32 <= char_val <= 126:
                        text += chr(char_val)
                    else:
                        text += f"[{char_val}]"
                except:
                    text += "?"
    
    print(f"Text length: {len(text)}")
    
    # Check if filler text is correct
    if text.startswith("In cybersecurity"):
        print("✓ Filler text decoded correctly")
        
        # Find the flag
        if "This flag is for you:" in text:
            marker_pos = text.find("This flag is for you:")
            after_marker = text[marker_pos + len("This flag is for you:"):]
            print(f"After marker: '{after_marker}'")
            
            # Look for tjctf pattern
            if "tjctf{" in after_marker:
                start = after_marker.find("tjctf{")
                end = after_marker.find("}", start) + 1
                if end > start:
                    flag = after_marker[start:end]
                    print(f"*** DECODED FLAG: {flag} ***")
            else:
                print("No tjctf{ pattern found in decoded text")
        else:
            print("Flag marker not found")
    else:
        print("✗ Filler text not decoded correctly")
        print(f"Start of text: '{text[:50]}'")
