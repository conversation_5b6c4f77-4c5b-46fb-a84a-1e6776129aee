import numpy as np

# Let me analyze the pattern more systematically
# I consistently get: ????f{uswnwv8931v{ww}
# This suggests the flag content might be: uswnwv8931vww (removing the extra {)

# But let me check if this could be some kind of encoding

# Setup and solve as before
with open("encoded.txt", "r") as f:
    lines = f.readlines()

encoded = []
for line in lines:
    row = list(map(int, line.strip().split()))
    encoded.append(row)

encoded = np.array(encoded, dtype=np.float64)

filler = "In cybersecurity, a CTF (Capture The Flag) challenge is a competitive, gamified event where participants, either individually or in teams, are tasked with finding and exploiting vulnerabilities in systems to capture hidden information known as flags. These flags are typically used to score points. CTFs test skills in areas like cryptography, web security, reverse engineering, and forensics, offering an exciting way to learn, practice, and showcase cybersecurity expertise.  This flag is for you: "

filler_bin = "".join([bin(ord(c))[2:].zfill(8) for c in filler])
n = 64
filler_bin += "0" * (n - len(filler_bin) % n)
filler_matrix = np.array([list(map(int, list(filler_bin[i:i+n]))) for i in range(0, len(filler_bin), n)], dtype=np.float64)

A = filler_matrix
B = encoded[:63]

# Solve
lambda_reg = 1e-12
AtA = A.T @ A
AtB = A.T @ B
AtA_reg = AtA + lambda_reg * np.eye(64)
key_matrix = np.linalg.solve(AtA_reg, AtB)

# Decode
key_inv = np.linalg.pinv(key_matrix)
decoded = encoded @ key_inv

# Get the consistent pattern
decoded_binary = (decoded > 0.3).astype(int)

text = ""
for row in decoded_binary:
    for i in range(0, 64, 8):
        byte_bits = row[i:i+8]
        byte_str = "".join(map(str, byte_bits))
        if len(byte_str) == 8:
            try:
                char_val = int(byte_str, 2)
                if 32 <= char_val <= 126:
                    text += chr(char_val)
                else:
                    text += "?"
            except:
                text += "?"

if "This flag is for you:" in text:
    marker_pos = text.find("This flag is for you:")
    after_marker = text[marker_pos + len("This flag is for you:"):]
    
    print(f"Consistent pattern: '{after_marker[:25]}'")
    
    # Extract the part that looks like flag content
    # Pattern: ????f{uswnwv8931v{ww}
    # The content between { and } appears to be: uswnwv8931v{ww
    # But there's an extra { which suggests corruption
    
    # Let me extract just the alphanumeric part
    import re
    
    # Look for the pattern after the first {
    if "{" in after_marker:
        brace_pos = after_marker.find("{")
        after_brace = after_marker[brace_pos+1:]
        
        # Extract alphanumeric characters until we hit the closing }
        flag_content = ""
        for char in after_brace:
            if char == "}":
                break
            elif char.isalnum():
                flag_content += char
        
        print(f"Extracted content: '{flag_content}'")
        
        # This gives me: uswnwv8931vww
        # Let me see if this could be some kind of cipher
        
        content = "uswnwv8931vww"
        print(f"Analyzing content: '{content}'")
        
        # Try ROT13
        def rot13(s):
            result = ""
            for c in s:
                if c.isalpha():
                    if c.islower():
                        result += chr((ord(c) - ord('a') + 13) % 26 + ord('a'))
                    else:
                        result += chr((ord(c) - ord('A') + 13) % 26 + ord('A'))
                else:
                    result += c
            return result
        
        print(f"ROT13: '{rot13(content)}'")
        
        # Try other Caesar shifts
        def caesar(s, shift):
            result = ""
            for c in s:
                if c.isalpha():
                    if c.islower():
                        result += chr((ord(c) - ord('a') + shift) % 26 + ord('a'))
                    else:
                        result += chr((ord(c) - ord('A') + shift) % 26 + ord('A'))
                else:
                    result += c
            return result
        
        print(f"Trying different Caesar shifts:")
        for shift in range(1, 26):
            shifted = caesar(content, shift)
            if any(word in shifted.lower() for word in ['dot', 'matrix', 'linear', 'numpy', 'math', 'product']):
                print(f"  Shift {shift}: '{shifted}' (contains relevant word!)")
        
        # Maybe it's a substitution cipher or the letters are just scrambled?
        # Let me see if I can find any patterns
        
        # Split into parts
        letters = ''.join([c for c in content if c.isalpha()])
        numbers = ''.join([c for c in content if c.isdigit()])
        
        print(f"Letters: '{letters}'")
        print(f"Numbers: '{numbers}'")
        
        # Maybe the letters spell something when rearranged?
        from itertools import permutations
        
        # Too many permutations, let me try a different approach
        # Maybe it's related to the challenge name "dotdotdot"
        
        # Let me check if the pattern could be interpreted differently
        # What if the corruption is more systematic?
        
        print(f"\nTrying to map to 'dotdotdot':")
        target = "dotdotdot"
        source = letters  # uswnwvvww
        
        print(f"Source letters: {source}")
        print(f"Target letters: {target}")
        
        # Maybe there's a simple substitution
        # u->d, s->o, w->t, n->d, w->o, v->t, v->d, w->o, w->t
        
        # Let me try a different approach - what if I just assume the flag is tjctf{dotdotdot}
        # and see if that makes sense given the corruption pattern I'm seeing
        
        assumed_flag = "tjctf{dotdotdot}"
        print(f"\nAssumed flag: {assumed_flag}")
        print("Corrupted pattern: ????f{uswnwv8931v{ww}")
        
        # The lengths don't match, which suggests my decoding is still off
        print(f"Assumed length: {len(assumed_flag)}")
        print(f"Corrupted length: {len('????f{uswnwv8931v{ww}')}")
        
        # Let me try one more approach - maybe the flag is actually what I decoded
        # but I need to interpret it correctly
        
        # What if the flag is actually tjctf{uswnwv8931vww} and that's meaningful somehow?
        possible_flag = f"tjctf{{{content}}}"
        print(f"\nPossible flag: {possible_flag}")
        
        # Or maybe the numbers are important: 8931
        # Could this be a hint or reference to something?
        
        print(f"Numbers in flag: 8931")
        print(f"Could this be a reference or code?")

else:
    print("Flag marker not found")
