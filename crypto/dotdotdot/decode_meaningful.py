#!/usr/bin/env python3

# We have decoded: tjctf{uswnwv8931vww}
# Now let's make it meaningful

decoded_flag = "tjctf{uswnwv8931vww}"
flag_content = "uswnwv8931vww"

print(f"Decoded flag: {decoded_flag}")
print(f"Flag content: {flag_content}")

# Let's analyze the content
letters = ''.join([c for c in flag_content if c.isalpha()])
numbers = ''.join([c for c in flag_content if c.isdigit()])

print(f"Letters: {letters}")
print(f"Numbers: {numbers}")

# Try different cipher approaches
print("\n=== Cipher Analysis ===")

# ROT13
def rot_n(text, n):
    result = ""
    for char in text:
        if char.isalpha():
            if char.islower():
                result += chr((ord(char) - ord('a') + n) % 26 + ord('a'))
            else:
                result += chr((ord(char) - ord('A') + n) % 26 + ord('A'))
        else:
            result += char
    return result

print("ROT ciphers:")
for n in range(1, 26):
    rotated = rot_n(letters, n)
    print(f"ROT{n}: {rotated}")
    # Check if it contains meaningful words
    if any(word in rotated.lower() for word in ['dot', 'matrix', 'linear', 'numpy', 'product', 'math']):
        print(f"  *** ROT{n} contains relevant word: {rotated} ***")

# Try reversing
print(f"\nReversed letters: {letters[::-1]}")

# Try splitting and rearranging
print(f"\nTrying to find patterns...")

# The challenge is about np.dot, so maybe it's related to "dot product"
target_words = ["dotproduct", "matrixdot", "numpydot", "dotdotdot"]

for target in target_words:
    print(f"\nTrying to map to '{target}':")
    if len(target) == len(letters):
        print(f"  Same length! {letters} -> {target}")
        # Create mapping
        mapping = {}
        for i, (src, dst) in enumerate(zip(letters, target)):
            if src in mapping and mapping[src] != dst:
                print(f"  Conflict: {src} maps to both {mapping[src]} and {dst}")
                break
            mapping[src] = dst
        else:
            print(f"  Mapping: {mapping}")
            # Apply mapping to full content
            mapped = ""
            for char in flag_content:
                if char.isalpha():
                    mapped += mapping.get(char, char)
                else:
                    mapped += char
            print(f"  Result: tjctf{{{mapped}}}")

# Try keyboard shift
print(f"\nKeyboard shift analysis:")
keyboard_rows = [
    "qwertyuiop",
    "asdfghjkl",
    "zxcvbnm"
]

# Try shifting each character
for shift in [-1, 1]:
    shifted = ""
    for char in letters:
        found = False
        for row in keyboard_rows:
            if char in row:
                idx = row.index(char)
                new_idx = (idx + shift) % len(row)
                shifted += row[new_idx]
                found = True
                break
        if not found:
            shifted += char
    print(f"Keyboard shift {shift}: {shifted}")

# Try Atbash cipher (A=Z, B=Y, etc.)
print(f"\nAtbash cipher:")
atbash = ""
for char in letters:
    if char.islower():
        atbash += chr(ord('z') - (ord(char) - ord('a')))
    else:
        atbash += chr(ord('Z') - (ord(char) - ord('A')))
print(f"Atbash: {atbash}")

# Try looking at the numbers as ASCII
print(f"\nNumbers as ASCII:")
nums = "8931"
for i in range(0, len(nums), 2):
    if i + 1 < len(nums):
        ascii_val = int(nums[i:i+2])
        if 32 <= ascii_val <= 126:
            print(f"{nums[i:i+2]} -> {chr(ascii_val)}")

# Try interpreting as coordinates or indices
print(f"\nNumbers as indices:")
print(f"8931 could be positions in alphabet:")
for digit in nums:
    pos = int(digit)
    if 1 <= pos <= 26:
        print(f"{digit} -> {chr(ord('a') + pos - 1)}")

# Maybe the letters are an anagram?
print(f"\nAnagram analysis:")
from collections import Counter
letter_count = Counter(letters)
print(f"Letter frequency: {letter_count}")

# Check if it's an anagram of common words
common_words = ["dotproduct", "matrixmath", "linearalgebra", "numpydot"]
for word in common_words:
    if Counter(word) == Counter(letters):
        print(f"*** ANAGRAM MATCH: {letters} is an anagram of {word} ***")

# Try substitution based on the challenge theme
print(f"\nTheme-based substitution:")
# The challenge is about numpy.dot, so maybe:
# u->d, s->o, w->t, etc.
theme_mapping = {
    'u': 'd',
    's': 'o', 
    'w': 't',
    'n': 'p',
    'v': 'r'
}

theme_result = ""
for char in letters:
    theme_result += theme_mapping.get(char, char)
print(f"Theme mapping: {theme_result}")

# Let me try a more systematic substitution approach
print(f"\n=== Systematic Substitution ===")

# I notice that "dotdotdot" has the same length as our letters
# Let me try to find a consistent mapping

source = "uswnwvvww"  # 9 letters
target = "dotdotdot"  # 9 letters

print(f"Source: {source}")
print(f"Target: {target}")

# Try to find a mapping that works
# Looking at patterns:
# u -> d
# s -> o
# w -> t (appears multiple times)
# n -> d (second occurrence)
# v -> o (appears twice)

# Let me check if this mapping is consistent
mapping = {}
for i, (s, t) in enumerate(zip(source, target)):
    if s in mapping:
        if mapping[s] != t:
            print(f"Conflict at position {i}: {s} already maps to {mapping[s]}, but needs {t}")
        else:
            print(f"Consistent: {s} -> {t}")
    else:
        mapping[s] = t
        print(f"New mapping: {s} -> {t}")

print(f"Final mapping: {mapping}")

# Apply this mapping to the full flag content
mapped_content = ""
for char in flag_content:
    if char.isalpha() and char in mapping:
        mapped_content += mapping[char]
    else:
        mapped_content += char

print(f"Mapped result: {mapped_content}")
print(f"Final flag: tjctf{{{mapped_content}}}")

# Let me also try the reverse - what if the numbers mean something?
print(f"\n=== Number Analysis ===")
numbers = "8931"

# Maybe the numbers indicate positions or shifts?
print(f"Numbers: {numbers}")

# Try using numbers as Caesar shift values
for i, digit in enumerate(numbers):
    shift = int(digit)
    if i < len(letters):
        char = letters[i]
        shifted = chr((ord(char) - ord('a') + shift) % 26 + ord('a'))
        print(f"Letter {i+1}: {char} + {shift} = {shifted}")

# Or maybe the numbers are ASCII values minus some offset?
print(f"Numbers as ASCII offsets:")
for num in [89, 31]:  # Split 8931 as 89,31
    if 32 <= num <= 126:
        print(f"{num} -> {chr(num)}")

# Try interpreting the whole thing differently
print(f"\n=== Alternative Interpretation ===")

# What if the pattern is actually telling us something about the substitution?
# The challenge is about "dotdotdot" and numpy.dot
# Maybe the flag is actually "dotdotdot" but encoded

# Let me try a simple Caesar cipher on "dotdotdot" to see if I get our pattern
test_word = "dotdotdot"
for shift in range(26):
    shifted_word = ""
    for char in test_word:
        shifted_word += chr((ord(char) - ord('a') + shift) % 26 + ord('a'))

    if shifted_word == letters:
        print(f"*** FOUND IT! Caesar shift {shift}: {test_word} -> {shifted_word} ***")
        print(f"*** MEANINGFUL FLAG: tjctf{{{test_word}}} ***")
        break

# If that doesn't work, try other common words
test_words = ["dotproduct", "matrixmath", "numpydot"]
for word in test_words:
    if len(word) == len(letters):
        for shift in range(26):
            shifted_word = ""
            for char in word:
                shifted_word += chr((ord(char) - ord('a') + shift) % 26 + ord('a'))

            if shifted_word == letters:
                print(f"*** FOUND IT! {word} with Caesar shift {shift} -> {shifted_word} ***")
                print(f"*** MEANINGFUL FLAG: tjctf{{{word}}} ***")
                break
