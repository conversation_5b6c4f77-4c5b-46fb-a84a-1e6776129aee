import numpy as np

# Try regularized solution

# Setup as before
with open("encoded.txt", "r") as f:
    lines = f.readlines()

encoded = []
for line in lines:
    row = list(map(int, line.strip().split()))
    encoded.append(row)

encoded = np.array(encoded, dtype=np.float64)

filler = "In cybersecurity, a CTF (Capture The Flag) challenge is a competitive, gamified event where participants, either individually or in teams, are tasked with finding and exploiting vulnerabilities in systems to capture hidden information known as flags. These flags are typically used to score points. CTFs test skills in areas like cryptography, web security, reverse engineering, and forensics, offering an exciting way to learn, practice, and showcase cybersecurity expertise.  This flag is for you: "

filler_bin = "".join([bin(ord(c))[2:].zfill(8) for c in filler])
n = 64
filler_bin += "0" * (n - len(filler_bin) % n)
filler_matrix = np.array([list(map(int, list(filler_bin[i:i+n]))) for i in range(0, len(filler_bin), n)], dtype=np.float64)

A = filler_matrix
B = encoded[:63]

# Try regularized least squares
lambda_reg = 1e-6
AtA = A.T @ A
AtA_reg = AtA + lambda_reg * np.eye(64)

try:
    key_matrix = np.linalg.solve(AtA_reg, A.T @ B)
    print("Using regularized solution")
    
    # Check reconstruction
    reconstruction = A @ key_matrix
    error = np.mean(np.abs(reconstruction - B))
    print(f"Reconstruction error: {error}")
    
    # Decode
    key_inv = np.linalg.pinv(key_matrix)
    decoded = encoded @ key_inv
    
    print(f"Decoded range: [{np.min(decoded):.3f}, {np.max(decoded):.3f}]")
    
    # Try threshold 0.5
    decoded_binary = (decoded > 0.5).astype(int)
    
    # Convert to text
    text = ""
    for row in decoded_binary:
        for i in range(0, 64, 8):
            byte_bits = row[i:i+8]
            byte_str = "".join(map(str, byte_bits))
            if len(byte_str) == 8:
                try:
                    char_val = int(byte_str, 2)
                    if 32 <= char_val <= 126:
                        text += chr(char_val)
                    else:
                        text += '?'
                except:
                    text += '?'
    
    print(f"Text length: {len(text)}")
    print(f"First 100 chars: {text[:100]}")
    
    # Check for flag
    if "tjctf{" in text:
        flag_start = text.find("tjctf{")
        flag_end = text.find("}", flag_start) + 1
        if flag_end > flag_start:
            flag = text[flag_start:flag_end]
            print(f"\n*** FLAG: {flag} ***")
    else:
        print(f"Last 100 chars: {text[-100:]}")
        
        # Look for flag pattern manually
        if "This flag is for you:" in text:
            marker_pos = text.find("This flag is for you:")
            after_marker = text[marker_pos + len("This flag is for you:"):]
            print(f"After marker: {after_marker[:50]}")
            
except Exception as e:
    print(f"Error: {e}")
