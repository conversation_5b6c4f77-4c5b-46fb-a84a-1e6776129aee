import numpy as np

# Let me analyze the flag area more carefully to see if I can get meaningful text

# Setup and solve
with open("encoded.txt", "r") as f:
    lines = f.readlines()

encoded = []
for line in lines:
    row = list(map(int, line.strip().split()))
    encoded.append(row)

encoded = np.array(encoded, dtype=np.float64)

filler = "In cybersecurity, a CTF (Capture The Flag) challenge is a competitive, gamified event where participants, either individually or in teams, are tasked with finding and exploiting vulnerabilities in systems to capture hidden information known as flags. These flags are typically used to score points. CTFs test skills in areas like cryptography, web security, reverse engineering, and forensics, offering an exciting way to learn, practice, and showcase cybersecurity expertise.  This flag is for you: "

filler_bin = "".join([bin(ord(c))[2:].zfill(8) for c in filler])
n = 64
filler_bin += "0" * (n - len(filler_bin) % n)
filler_matrix = np.array([list(map(int, list(filler_bin[i:i+n]))) for i in range(0, len(filler_bin), n)], dtype=np.float64)

A = filler_matrix
B = encoded[:63]

# Regularized solution
lambda_reg = 1e-6
AtA = A.T @ A
AtA_reg = AtA + lambda_reg * np.eye(64)
key_matrix = np.linalg.solve(AtA_reg, A.T @ B)

# Decode
key_inv = np.linalg.pinv(key_matrix)
decoded = encoded @ key_inv

print("Analyzing the flag area (last few rows)...")

# The flag should start around row 63 (0-indexed)
for row_idx in range(63, min(66, decoded.shape[0])):
    print(f"\nRow {row_idx}:")
    row = decoded[row_idx]
    
    # Show raw values for first 32 bits (4 characters)
    print("Raw values (first 32 bits):")
    for i in range(32):
        print(f"{row[i]:6.3f}", end=" ")
        if (i + 1) % 8 == 0:
            print()
    
    # Try different thresholds for this row
    print("\nDifferent thresholds:")
    for thresh in [0.3, 0.4, 0.5, 0.6, 0.7]:
        binary_row = (row > thresh).astype(int)
        chars = ""
        for i in range(0, min(32, 64), 8):  # First 4 characters
            byte_bits = binary_row[i:i+8]
            byte_str = "".join(map(str, byte_bits))
            if len(byte_str) == 8:
                try:
                    char_val = int(byte_str, 2)
                    if 32 <= char_val <= 126:
                        chars += chr(char_val)
                    else:
                        chars += f"[{char_val}]"
                except:
                    chars += "?"
        print(f"  {thresh}: {chars}")

# Let me also try to see if there are common English words that could fit
print("\n" + "="*50)
print("Looking for meaningful patterns...")

# Try different thresholds on the entire text
for thresh in [0.4, 0.45, 0.5, 0.55, 0.6]:
    print(f"\nThreshold {thresh}:")
    decoded_binary = (decoded > thresh).astype(int)
    
    text = ""
    for row in decoded_binary:
        for i in range(0, 64, 8):
            byte_bits = row[i:i+8]
            byte_str = "".join(map(str, byte_bits))
            if len(byte_str) == 8:
                try:
                    char_val = int(byte_str, 2)
                    if 32 <= char_val <= 126:
                        text += chr(char_val)
                    else:
                        text += '?'
                except:
                    text += '?'
    
    # Extract flag area
    if "This flag is for you:" in text:
        marker_pos = text.find("This flag is for you:")
        after_marker = text[marker_pos + len("This flag is for you:"):]
        flag_area = after_marker[:30]  # First 30 chars after marker
        print(f"  Flag area: '{flag_area}'")
        
        # Look for any recognizable patterns
        if "tjctf{" in flag_area:
            start = flag_area.find("tjctf{")
            end = flag_area.find("}", start) + 1
            if end > start:
                potential_flag = flag_area[start:end]
                print(f"  *** Potential flag: {potential_flag} ***")
