#!/usr/bin/env python3

# Focus on the numbers as the key to decoding
# We have: tjctf{uswnwv8931vww}

decoded_flag = "tjctf{uswnwv8931vww}"
flag_content = "uswnwv8931vww"

print(f"Decoded flag: {decoded_flag}")

# Split into parts
letters_before = "uswnwv"
numbers = "8931"
letters_after = "vww"

print(f"Letters before numbers: {letters_before}")
print(f"Numbers: {numbers}")
print(f"Letters after numbers: {letters_after}")

# Try using the numbers as shift values for the letters
print(f"\n=== Using numbers as shift values ===")

# Method 1: Apply each digit to corresponding letter
shifts = [int(d) for d in numbers]
print(f"Shifts: {shifts}")

# Apply to letters before numbers
result1 = ""
for i, char in enumerate(letters_before):
    if i < len(shifts):
        shift = shifts[i]
        shifted = chr((ord(char) - ord('a') + shift) % 26 + ord('a'))
        result1 += shifted
        print(f"{char} + {shift} = {shifted}")
    else:
        result1 += char

print(f"Result for letters before: {result1}")

# Method 2: Use numbers as a repeating key
print(f"\n=== Using numbers as repeating key ===")
all_letters = letters_before + letters_after
result2 = ""

for i, char in enumerate(all_letters):
    shift = int(numbers[i % len(numbers)])
    shifted = chr((ord(char) - ord('a') + shift) % 26 + ord('a'))
    result2 += shifted
    print(f"{char} + {shift} = {shifted}")

print(f"Result: {result2}")

# Method 3: Try subtracting instead of adding
print(f"\n=== Subtracting shifts ===")
result3 = ""

for i, char in enumerate(all_letters):
    shift = int(numbers[i % len(numbers)])
    shifted = chr((ord(char) - ord('a') - shift) % 26 + ord('a'))
    result3 += shifted
    print(f"{char} - {shift} = {shifted}")

print(f"Result: {result3}")

# Method 4: What if the numbers represent positions in the alphabet?
print(f"\n=== Numbers as alphabet positions ===")
for digit in numbers:
    pos = int(digit)
    if 1 <= pos <= 26:
        letter = chr(ord('a') + pos - 1)
        print(f"{digit} -> position {pos} -> {letter}")

# Method 5: Try Vigenère cipher with numbers as key
print(f"\n=== Vigenère cipher ===")
key = numbers
plaintext = all_letters

def vigenere_decrypt(ciphertext, key):
    result = ""
    key_repeated = (key * (len(ciphertext) // len(key) + 1))[:len(ciphertext)]
    
    for i, char in enumerate(ciphertext):
        shift = int(key_repeated[i])
        decrypted = chr((ord(char) - ord('a') - shift) % 26 + ord('a'))
        result += decrypted
    
    return result

vigenere_result = vigenere_decrypt(all_letters, numbers)
print(f"Vigenère decrypt: {vigenere_result}")

# Check if any of these results look meaningful
results = [result1, result2, result3, vigenere_result]
meaningful_words = ["dot", "matrix", "numpy", "linear", "product", "math"]

print(f"\n=== Checking for meaningful results ===")
for i, result in enumerate(results, 1):
    print(f"Result {i}: {result}")
    for word in meaningful_words:
        if word in result:
            print(f"  *** Contains '{word}' - this might be it! ***")
            print(f"  *** Potential flag: tjctf{{{result}}} ***")

# Method 6: What if it's a simple substitution where numbers indicate the pattern?
print(f"\n=== Pattern-based substitution ===")

# Maybe 8931 tells us something about the substitution pattern
# Let's try mapping based on the challenge theme

# The challenge is "dotdotdot" - maybe that's the target
target = "dotdotdot"
source = all_letters

if len(source) == len(target):
    print(f"Same length: {source} -> {target}")
    
    # Create mapping
    mapping = {}
    for s, t in zip(source, target):
        if s in mapping and mapping[s] != t:
            print(f"Conflict: {s} maps to both {mapping[s]} and {t}")
        else:
            mapping[s] = t
    
    print(f"Mapping: {mapping}")
    
    # Apply to full content
    mapped = ""
    for char in flag_content:
        if char.isalpha():
            mapped += mapping.get(char, char)
        else:
            mapped += char
    
    print(f"Mapped flag content: {mapped}")
    print(f"Final flag: tjctf{{{mapped}}}")

# Method 7: Maybe the numbers are red herrings and it's just a simple cipher
print(f"\n=== Simple cipher on letters only ===")

# Try Caesar cipher on just the letters
letters_only = ''.join([c for c in flag_content if c.isalpha()])
print(f"Letters only: {letters_only}")

for shift in range(1, 26):
    shifted = ""
    for char in letters_only:
        shifted += chr((ord(char) - ord('a') + shift) % 26 + ord('a'))
    
    # Check if this looks like a meaningful word
    if any(word in shifted for word in ["dotdotdot", "dotproduct", "matrixdot"]):
        print(f"*** Caesar shift {shift}: {letters_only} -> {shifted} ***")
        print(f"*** This contains a meaningful word! ***")
        print(f"*** Potential flag: tjctf{{{shifted}}} ***")
