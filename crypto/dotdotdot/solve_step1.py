import numpy as np

# Step 1: Set up the known data

# Read encoded data
with open("encoded.txt", "r") as f:
    lines = f.readlines()

encoded = []
for line in lines:
    row = list(map(int, line.strip().split()))
    encoded.append(row)

encoded = np.array(encoded, dtype=np.float64)
print(f"Encoded shape: {encoded.shape}")

# Set up filler matrix (known plaintext)
filler = "In cybersecurity, a CTF (Capture The Flag) challenge is a competitive, gamified event where participants, either individually or in teams, are tasked with finding and exploiting vulnerabilities in systems to capture hidden information known as flags. These flags are typically used to score points. CTFs test skills in areas like cryptography, web security, reverse engineering, and forensics, offering an exciting way to learn, practice, and showcase cybersecurity expertise.  This flag is for you: "

# Convert to binary exactly as in encode.py
filler_bin = "".join([bin(ord(c))[2:].zfill(8) for c in filler])
n = 64
filler_bin += "0" * (n - len(filler_bin) % n)
filler_matrix = np.array([list(map(int, list(filler_bin[i:i+n]))) for i in range(0, len(filler_bin), n)], dtype=np.float64)

print(f"Filler matrix shape: {filler_matrix.shape}")
print(f"We have {filler_matrix.shape[0]} known rows out of {encoded.shape[0]} total rows")

# The relationship is: filler_matrix @ key = encoded[:63]
# We need to solve for key, then use it to decode the flag rows

# Step 2: Solve for the key matrix using least squares
A = filler_matrix  # 63 x 64
B = encoded[:63]   # 63 x 64

print(f"Solving: A @ key = B where A is {A.shape} and B is {B.shape}")

# Use pseudoinverse since A is not square
A_pinv = np.linalg.pinv(A)
key_matrix = A_pinv @ B

print(f"Key matrix shape: {key_matrix.shape}")

# Verify the solution
reconstruction = A @ key_matrix
error = np.mean(np.abs(reconstruction - B))
print(f"Reconstruction error: {error}")

if error < 1e-3:
    print("Key recovery looks good!")

    # Step 3: Decode all rows
    # For decoding: original_row = encoded_row @ key_matrix^(-1)

    try:
        key_inv = np.linalg.inv(key_matrix)
        print("Key matrix is invertible")
    except:
        print("Using pseudoinverse for key matrix")
        key_inv = np.linalg.pinv(key_matrix)

    # Decode all rows
    decoded = encoded @ key_inv
    print(f"Decoded shape: {decoded.shape}")

    # Convert to binary (round to 0 or 1)
    decoded_binary = np.round(decoded).astype(int)
    decoded_binary = np.clip(decoded_binary, 0, 1)

    print("Decoding successful!")

    # Step 4: Convert binary to text
    decoded_text = ""
    for row in decoded_binary:
        for i in range(0, 64, 8):  # Process 8 bits at a time
            byte_bits = row[i:i+8]
            byte_str = "".join(map(str, byte_bits))
            if len(byte_str) == 8:
                try:
                    char_val = int(byte_str, 2)
                    if 32 <= char_val <= 126:  # Printable ASCII
                        decoded_text += chr(char_val)
                    else:
                        decoded_text += '?'  # Non-printable
                except:
                    decoded_text += '?'

    print(f"Decoded text length: {len(decoded_text)}")
    print(f"First 100 characters: {decoded_text[:100]}")

    # Look for the flag
    if "tjctf{" in decoded_text:
        flag_start = decoded_text.find("tjctf{")
        flag_end = decoded_text.find("}", flag_start) + 1
        if flag_end > flag_start:
            flag = decoded_text[flag_start:flag_end]
            print(f"\n*** FLAG FOUND: {flag} ***")
        else:
            print("Found tjctf{ but no closing brace")
    else:
        print("No tjctf{ pattern found")
        # Show the end of the text where flag should be
        print(f"End of text: ...{decoded_text[-100:]}")

else:
    print("High error - key recovery might be problematic")
