#!/usr/bin/env python3

# Let's explore the promising results further

decoded_flag = "tjctf{uswnwv8931vww}"
flag_content = "uswnwv8931vww"

# I got "mjtmomsvo" when subtracting shifts - let me analyze this
result = "mjtmomsvo"
print(f"Subtraction result: {result}")

# Let me try different interpretations of this
print(f"\n=== Analyzing 'mjtmomsvo' ===")

# Maybe it's still encoded? Try ROT13
def rot_n(text, n):
    result = ""
    for char in text:
        if char.isalpha():
            result += chr((ord(char) - ord('a') + n) % 26 + ord('a'))
        else:
            result += char
    return result

for n in range(1, 26):
    rotated = rot_n(result, n)
    print(f"ROT{n}: {rotated}")
    if any(word in rotated for word in ["dot", "matrix", "numpy", "product"]):
        print(f"  *** Contains meaningful word! ***")

# Let me try a different approach - what if the numbers split the string differently?
print(f"\n=== Different number interpretation ===")

# What if 8931 means something else?
# Maybe it's telling us to take every 8th, 9th, 3rd, 1st character?
# Or maybe it's positions to extract?

letters = ''.join([c for c in flag_content if c.isalpha()])
print(f"All letters: {letters}")

# Try extracting positions 8,9,3,1 (1-indexed)
positions = [8, 9, 3, 1]
extracted = ""
for pos in positions:
    if pos <= len(letters):
        extracted += letters[pos-1]  # Convert to 0-indexed
        print(f"Position {pos}: {letters[pos-1]}")

print(f"Extracted: {extracted}")

# Maybe the numbers are a key for a different cipher
print(f"\n=== Numbers as cipher key ===")

# Try using 8931 as a numeric key for a more complex substitution
key = "8931"

# Method: Use each digit to shift the corresponding group of letters
groups = ["uswnwv", "vww"]  # Split around the numbers
print(f"Groups: {groups}")

# Apply shifts to each group
for i, group in enumerate(groups):
    if i < len(key):
        shift = int(key[i])
        shifted_group = ""
        for char in group:
            shifted_group += chr((ord(char) - ord('a') + shift) % 26 + ord('a'))
        print(f"Group {i+1} with shift {shift}: {group} -> {shifted_group}")

# Let me try a completely different approach
print(f"\n=== Reverse engineering approach ===")

# What if I work backwards from known meaningful flags?
target_flags = ["dotdotdot", "dotproduct", "matrixmath", "numpydot"]

for target in target_flags:
    print(f"\nTrying to reverse engineer to '{target}':")
    
    if len(target) == len(letters):
        print(f"  Same length as letters: {letters}")
        
        # Try to find what cipher would transform target -> letters
        for shift in range(26):
            shifted_target = ""
            for char in target:
                shifted_target += chr((ord(char) - ord('a') + shift) % 26 + ord('a'))
            
            if shifted_target == letters:
                print(f"  *** FOUND: Caesar shift {shift} transforms {target} -> {letters} ***")
                print(f"  *** So the flag should be: tjctf{{{target}}} ***")
                break

# Let me also try Atbash and other simple ciphers on the letters
print(f"\n=== Other ciphers on letters ===")

# Atbash (A=Z, B=Y, etc.)
atbash = ""
for char in letters:
    atbash += chr(ord('z') - (ord(char) - ord('a')))
print(f"Atbash: {atbash}")

# Reverse
reversed_letters = letters[::-1]
print(f"Reversed: {reversed_letters}")

# Maybe the flag is actually simpler and I'm overcomplicating it
print(f"\n=== Simple interpretation ===")

# What if the decoded flag is actually correct but needs minor fixes?
# The pattern tjctf{uswnwv8931vww} might just need the numbers removed
letters_only = ''.join([c for c in flag_content if c.isalpha()])
print(f"Letters only: {letters_only}")

# Check if this is an anagram of something meaningful
from collections import Counter

target_words = ["dotdotdot", "dotproduct", "matrixmath"]
for word in target_words:
    if Counter(word) == Counter(letters_only):
        print(f"*** {letters_only} is an anagram of {word}! ***")
        print(f"*** Flag should be: tjctf{{{word}}} ***")

# Let me try one more approach - what if it's a keyboard shift?
print(f"\n=== Keyboard shift ===")

qwerty = "qwertyuiopasdfghjklzxcvbnm"
shifted_keyboard = ""

for char in letters:
    if char in qwerty:
        idx = qwerty.index(char)
        # Try shifting by 1 position
        new_idx = (idx + 1) % len(qwerty)
        shifted_keyboard += qwerty[new_idx]
    else:
        shifted_keyboard += char

print(f"Keyboard shift +1: {shifted_keyboard}")

# Try shift -1
shifted_keyboard_back = ""
for char in letters:
    if char in qwerty:
        idx = qwerty.index(char)
        new_idx = (idx - 1) % len(qwerty)
        shifted_keyboard_back += qwerty[new_idx]
    else:
        shifted_keyboard_back += char

print(f"Keyboard shift -1: {shifted_keyboard_back}")

# Final attempt - maybe it's related to the specific challenge
print(f"\n=== Challenge-specific interpretation ===")
print(f"Challenge name: dotdotdot")
print(f"Challenge hint: np.dot")
print(f"Most likely flag: tjctf{{dotdotdot}}")
print(f"Let me verify if our decoded string could encode this...")

# Check if "dotdotdot" with some cipher gives us our letters
target = "dotdotdot"
for shift in range(26):
    encoded = ""
    for char in target:
        encoded += chr((ord(char) - ord('a') + shift) % 26 + ord('a'))
    
    if encoded == letters_only:
        print(f"*** VERIFIED: Caesar shift {shift} on '{target}' gives '{letters_only}' ***")
        print(f"*** Therefore, the flag is: tjctf{{{target}}} ***")
        break
else:
    print("No simple Caesar cipher found")

# Let me try one more systematic approach
print(f"\n=== Systematic number-based decoding ===")

# What if the numbers 8931 tell us exactly how to decode?
# Let me try applying each digit as a shift to groups of letters

original = "uswnwvvww"
numbers = "8931"

# Method 1: Apply each number to each letter cyclically
print("Method 1: Cyclic application")
result1 = ""
for i, char in enumerate(original):
    shift = int(numbers[i % len(numbers)])
    # Try both addition and subtraction
    shifted_add = chr((ord(char) - ord('a') + shift) % 26 + ord('a'))
    shifted_sub = chr((ord(char) - ord('a') - shift) % 26 + ord('a'))
    result1 += shifted_sub  # Use subtraction since it gave better results before
    print(f"{char} - {shift} = {shifted_sub}")

print(f"Result 1: {result1}")

# Method 2: Split into 4 groups based on the 4 digits
print("\nMethod 2: Split into groups")
group_size = len(original) // len(numbers)
remainder = len(original) % len(numbers)

groups = []
start = 0
for i, digit in enumerate(numbers):
    size = group_size + (1 if i < remainder else 0)
    group = original[start:start + size]
    groups.append(group)
    start += size

print(f"Groups: {groups}")

result2 = ""
for i, (group, digit) in enumerate(zip(groups, numbers)):
    shift = int(digit)
    shifted_group = ""
    for char in group:
        shifted_char = chr((ord(char) - ord('a') - shift) % 26 + ord('a'))
        shifted_group += shifted_char
    result2 += shifted_group
    print(f"Group {i+1}: {group} - {shift} = {shifted_group}")

print(f"Result 2: {result2}")

# Check if either result is meaningful
results = [result1, result2]
for i, result in enumerate(results, 1):
    print(f"\nChecking result {i}: {result}")

    # Check if it's an anagram of meaningful words
    from collections import Counter
    result_counter = Counter(result)

    meaningful_words = ["dotdotdot", "dotproduct", "matrixmath", "numpydot", "linearalgebra"]
    for word in meaningful_words:
        if Counter(word) == result_counter:
            print(f"*** ANAGRAM MATCH: {result} is an anagram of {word} ***")
            print(f"*** FLAG: tjctf{{{word}}} ***")

    # Check if it contains meaningful substrings
    if "dot" in result:
        print(f"*** Contains 'dot': {result} ***")
    if "matrix" in result:
        print(f"*** Contains 'matrix': {result} ***")

# One final attempt - what if I need to be more creative with the numbers?
print(f"\n=== Creative number interpretation ===")

# What if 8931 represents something else entirely?
# Maybe it's a date, coordinate, or reference?

# Or maybe the numbers should be interpreted as a single instruction
print(f"Numbers as single value: 8931")

# Try using 8931 as a single shift value
single_shift = 8931 % 26  # Reduce to alphabet size
print(f"8931 mod 26 = {single_shift}")

result3 = ""
for char in original:
    shifted = chr((ord(char) - ord('a') - single_shift) % 26 + ord('a'))
    result3 += shifted

print(f"Single shift result: {result3}")

# Maybe the numbers indicate positions to rearrange?
print(f"\nRearrangement based on 8931:")
if len(original) >= 4:
    # Use 8931 as positions (1-indexed)
    positions = [8, 9, 3, 1]
    rearranged = ""
    for pos in positions:
        if pos <= len(original):
            rearranged += original[pos-1]
    print(f"Rearranged: {rearranged}")

print(f"\n=== SUMMARY ===")
print(f"Original decoded: {decoded_flag}")
print(f"Most promising interpretations:")
print(f"1. Simple substitution to 'dotdotdot' (theme-based)")
print(f"2. Number-based decoding results: {result1}, {result2}")
print(f"3. The flag is likely: tjctf{{dotdotdot}} based on challenge theme")
