import numpy as np

# Let's understand the basic structure first

# Read encoded data
with open("encoded.txt", "r") as f:
    lines = f.readlines()

print(f"Number of encoded rows: {len(lines)}")

# Parse first few rows to understand structure
encoded_sample = []
for i, line in enumerate(lines[:5]):
    row = list(map(int, line.strip().split()))
    encoded_sample.append(row)
    print(f"Row {i+1}: {len(row)} values, range [{min(row)}, {max(row)}]")

# Check the filler text
filler = "In cybersecurity, a CTF (Capture The Flag) challenge is a competitive, gamified event where participants, either individually or in teams, are tasked with finding and exploiting vulnerabilities in systems to capture hidden information known as flags. These flags are typically used to score points. CTFs test skills in areas like cryptography, web security, reverse engineering, and forensics, offering an exciting way to learn, practice, and showcase cybersecurity expertise.  This flag is for you: "

print(f"\nFiller text length: {len(filler)} characters")
print(f"Filler in bits: {len(filler) * 8} bits")
print(f"Filler in 64-bit rows: {len(filler) * 8 / 64:.1f} rows")

# Convert filler to binary as in encode.py
filler_bin = "".join([bin(ord(c))[2:].zfill(8) for c in filler])
n = 64
filler_bin_padded = filler_bin + "0" * (n - len(filler_bin) % n)
filler_rows = len(filler_bin_padded) // n

print(f"Filler after padding: {len(filler_bin_padded)} bits = {filler_rows} rows")
print(f"So the flag should start around row {filler_rows + 1}")
