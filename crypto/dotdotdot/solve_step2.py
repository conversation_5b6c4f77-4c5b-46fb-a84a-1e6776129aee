import numpy as np

# Try different thresholds to get better decoding

# Read and solve as before
with open("encoded.txt", "r") as f:
    lines = f.readlines()

encoded = []
for line in lines:
    row = list(map(int, line.strip().split()))
    encoded.append(row)

encoded = np.array(encoded, dtype=np.float64)

# Filler setup
filler = "In cybersecurity, a CTF (Capture The Flag) challenge is a competitive, gamified event where participants, either individually or in teams, are tasked with finding and exploiting vulnerabilities in systems to capture hidden information known as flags. These flags are typically used to score points. CTFs test skills in areas like cryptography, web security, reverse engineering, and forensics, offering an exciting way to learn, practice, and showcase cybersecurity expertise.  This flag is for you: "

filler_bin = "".join([bin(ord(c))[2:].zfill(8) for c in filler])
n = 64
filler_bin += "0" * (n - len(filler_bin) % n)
filler_matrix = np.array([list(map(int, list(filler_bin[i:i+n]))) for i in range(0, len(filler_bin), n)], dtype=np.float64)

# Solve for key
A = filler_matrix
B = encoded[:63]
A_pinv = np.linalg.pinv(A)
key_matrix = A_pinv @ B

# Decode
key_inv = np.linalg.pinv(key_matrix)
decoded = encoded @ key_inv

print(f"Decoded value range: [{np.min(decoded):.3f}, {np.max(decoded):.3f}]")

# Try different thresholds
for threshold in [0.3, 0.4, 0.5, 0.6, 0.7]:
    print(f"\n--- Threshold {threshold} ---")
    
    decoded_binary = (decoded > threshold).astype(int)
    
    # Convert to text
    text = ""
    for row in decoded_binary:
        for i in range(0, 64, 8):
            byte_bits = row[i:i+8]
            byte_str = "".join(map(str, byte_bits))
            if len(byte_str) == 8:
                try:
                    char_val = int(byte_str, 2)
                    if 32 <= char_val <= 126:
                        text += chr(char_val)
                    else:
                        text += '?'
                except:
                    text += '?'
    
    # Check if filler text is recognizable
    if "cybersecurity" in text and "CTF" in text:
        print("✓ Filler text looks good")
        
        # Look for flag
        if "tjctf{" in text:
            flag_start = text.find("tjctf{")
            flag_end = text.find("}", flag_start) + 1
            if flag_end > flag_start:
                flag = text[flag_start:flag_end]
                print(f"*** FLAG: {flag} ***")
            else:
                print("Found tjctf{ but no closing brace")
        else:
            # Show end of text
            print(f"End: ...{text[-50:]}")
    else:
        print("✗ Filler text corrupted")
        print(f"Start: {text[:50]}")
