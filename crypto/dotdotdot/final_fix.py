import numpy as np

# Final attempt to get the correct flag

# Setup and solve
with open("encoded.txt", "r") as f:
    lines = f.readlines()

encoded = []
for line in lines:
    row = list(map(int, line.strip().split()))
    encoded.append(row)

encoded = np.array(encoded, dtype=np.float64)

filler = "In cybersecurity, a CTF (Capture The Flag) challenge is a competitive, gamified event where participants, either individually or in teams, are tasked with finding and exploiting vulnerabilities in systems to capture hidden information known as flags. These flags are typically used to score points. CTFs test skills in areas like cryptography, web security, reverse engineering, and forensics, offering an exciting way to learn, practice, and showcase cybersecurity expertise.  This flag is for you: "

filler_bin = "".join([bin(ord(c))[2:].zfill(8) for c in filler])
n = 64
filler_bin += "0" * (n - len(filler_bin) % n)
filler_matrix = np.array([list(map(int, list(filler_bin[i:i+n]))) for i in range(0, len(filler_bin), n)], dtype=np.float64)

A = filler_matrix
B = encoded[:63]

# Solve
lambda_reg = 1e-12
AtA = A.T @ A
AtB = A.T @ B
AtA_reg = AtA + lambda_reg * np.eye(64)
key_matrix = np.linalg.solve(AtA_reg, AtB)

# Decode
key_inv = np.linalg.pinv(key_matrix)
decoded = encoded @ key_inv

print("Analyzing the exact bit pattern for the first character:")

# The first character starts at row 63, bit 0
first_char_bits = decoded[63, :8]
print("Raw values for first character:")
for i, val in enumerate(first_char_bits):
    print(f"  Bit {i}: {val:8.6f}")

# Current pattern: 01100110 = 'f' (102)
# Target pattern:  01110100 = 't' (116)
# Difference: bit 3 should be 1 instead of 0

print(f"\nCurrent binary: {''.join(['1' if v > 0.3 else '0' for v in first_char_bits])}")
print(f"Target binary:  01110100")
print(f"Current char:   {chr(int(''.join(['1' if v > 0.3 else '0' for v in first_char_bits]), 2))}")
print(f"Target char:    t")

# The issue is bit 3 has value -0.000 which becomes 0, but should be 1
bit_3_value = first_char_bits[3]
print(f"\nBit 3 value: {bit_3_value}")

# Let me try manually setting this bit to 1
decoded_fixed = decoded.copy()

# Convert to binary with threshold 0.3
decoded_binary = (decoded > 0.3).astype(int)

# Manually fix bit 3 of the first character
decoded_binary[63, 3] = 1

print(f"Fixed binary:   {''.join(map(str, decoded_binary[63, :8]))}")
print(f"Fixed char:     {chr(int(''.join(map(str, decoded_binary[63, :8])), 2))}")

# Convert the entire fixed result to text
text_fixed = ""
for row in decoded_binary:
    for i in range(0, 64, 8):
        byte_bits = row[i:i+8]
        byte_str = "".join(map(str, byte_bits))
        if len(byte_str) == 8:
            try:
                char_val = int(byte_str, 2)
                if 32 <= char_val <= 126:
                    text_fixed += chr(char_val)
                else:
                    text_fixed += "?"
            except:
                text_fixed += "?"

print(f"\nChecking if filler text is still correct:")
if text_fixed.startswith("In cybersecurity"):
    print("✓ Filler text is correct")
else:
    print("✗ Filler text corrupted")
    print(f"Start: {text_fixed[:50]}")

# Extract the flag
if "This flag is for you:" in text_fixed:
    marker_pos = text_fixed.find("This flag is for you:")
    after_marker = text_fixed[marker_pos + len("This flag is for you:"):]
    print(f"\nAfter marker: '{after_marker[:30]}'")
    
    if "tjctf{" in after_marker:
        start = after_marker.find("tjctf{")
        end = after_marker.find("}", start) + 1
        if end > start:
            flag = after_marker[start:end]
            print(f"\n*** FINAL FLAG: {flag} ***")
        else:
            print("Found tjctf{ but no closing brace")
    else:
        print("Still no tjctf{ pattern found")
        
        # Maybe there are other corrupted bits
        print("Checking for other issues...")
        
        # Look at the next few characters
        print("Next few characters after the fix:")
        for i in range(5):
            char_start = i * 8
            char_bits = decoded_binary[63, char_start:char_start+8]
            char_str = "".join(map(str, char_bits))
            if len(char_str) == 8:
                try:
                    char_val = int(char_str, 2)
                    char = chr(char_val) if 32 <= char_val <= 126 else f"[{char_val}]"
                    print(f"  Char {i+1}: {char_str} = '{char}'")
                except:
                    print(f"  Char {i+1}: {char_str} = ?")
else:
    print("Flag marker not found")
