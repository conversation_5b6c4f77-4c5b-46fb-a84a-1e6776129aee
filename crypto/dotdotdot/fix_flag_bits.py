import numpy as np

# Try to fix the corrupted flag bits by analyzing the pattern

# Setup and solve as before
with open("encoded.txt", "r") as f:
    lines = f.readlines()

encoded = []
for line in lines:
    row = list(map(int, line.strip().split()))
    encoded.append(row)

encoded = np.array(encoded, dtype=np.float64)

filler = "In cybersecurity, a CTF (Capture The Flag) challenge is a competitive, gamified event where participants, either individually or in teams, are tasked with finding and exploiting vulnerabilities in systems to capture hidden information known as flags. These flags are typically used to score points. CTFs test skills in areas like cryptography, web security, reverse engineering, and forensics, offering an exciting way to learn, practice, and showcase cybersecurity expertise.  This flag is for you: "

filler_bin = "".join([bin(ord(c))[2:].zfill(8) for c in filler])
n = 64
filler_bin += "0" * (n - len(filler_bin) % n)
filler_matrix = np.array([list(map(int, list(filler_bin[i:i+n]))) for i in range(0, len(filler_bin), n)], dtype=np.float64)

A = filler_matrix
B = encoded[:63]

# Use the best regularization from before
lambda_reg = 1e-12
AtA = A.T @ A
AtB = A.T @ B
AtA_reg = AtA + lambda_reg * np.eye(64)
key_matrix = np.linalg.solve(AtA_reg, AtB)

# Decode
key_inv = np.linalg.pinv(key_matrix)
decoded = encoded @ key_inv

print("Analyzing the flag area in detail...")

# Find where the flag starts (after the filler)
flag_start_row = 63  # Should be around here

# Look at the raw decoded values for the flag area
print(f"Raw decoded values for flag area:")
for row_idx in range(63, min(66, decoded.shape[0])):
    print(f"\nRow {row_idx}:")
    row = decoded[row_idx]
    
    # Show first 40 bits (5 characters)
    for i in range(40):
        print(f"{row[i]:6.3f}", end=" ")
        if (i + 1) % 8 == 0:
            print(f" (char {(i+1)//8})")

# Now let me try to manually fix the corrupted characters
print(f"\n" + "="*50)
print("Manual flag reconstruction:")

# I consistently see the pattern: ????f{uswnwv8931v{ww}
# This should be: tjctf{...}

# Let me try to decode with threshold 0.3 and then manually fix the first few characters
decoded_binary = (decoded > 0.3).astype(int)

# Convert to text
text = ""
for row in decoded_binary:
    for i in range(0, 64, 8):
        byte_bits = row[i:i+8]
        byte_str = "".join(map(str, byte_bits))
        if len(byte_str) == 8:
            try:
                char_val = int(byte_str, 2)
                if 32 <= char_val <= 126:
                    text += chr(char_val)
                else:
                    text += "?"
            except:
                text += "?"

# Find the flag area
if "This flag is for you:" in text:
    marker_pos = text.find("This flag is for you:")
    after_marker = text[marker_pos + len("This flag is for you:"):]
    
    print(f"Corrupted flag area: '{after_marker[:25]}'")
    
    # I see patterns like: ????f{uswnwv8931v{ww}
    # Let me try to extract the meaningful part
    
    # Find the { character
    if "{" in after_marker:
        brace_start = after_marker.find("{")
        brace_end = after_marker.find("}", brace_start)
        
        if brace_end > brace_start:
            # Extract the content between braces
            flag_content = after_marker[brace_start+1:brace_end]
            print(f"Flag content: '{flag_content}'")
            
            # The content looks like: uswnwv8931v{ww
            # But there's an extra { in there, which suggests corruption
            
            # Let me try to clean this up
            # Remove any extra { characters from the content
            clean_content = flag_content.replace("{", "")
            print(f"Cleaned content: '{clean_content}'")
            
            # Reconstruct the flag
            reconstructed_flag = f"tjctf{{{clean_content}}}"
            print(f"Reconstructed flag: {reconstructed_flag}")
            
            # But this still doesn't look meaningful...
            # Let me try a different approach
            
            # Maybe I need to look at the bit patterns more carefully
            print(f"\nTrying bit-level analysis...")
            
            # Look at the exact bit pattern around the flag
            flag_row_start = 63
            flag_bits = ""
            
            for row_idx in range(flag_row_start, min(flag_row_start + 3, decoded.shape[0])):
                row = decoded[row_idx]
                for bit_val in row:
                    if bit_val > 0.3:
                        flag_bits += "1"
                    else:
                        flag_bits += "0"
            
            print(f"Flag bits: {flag_bits[:128]}")  # First 16 characters
            
            # Convert to characters
            flag_chars = ""
            for i in range(0, min(128, len(flag_bits)), 8):
                byte_str = flag_bits[i:i+8]
                if len(byte_str) == 8:
                    try:
                        char_val = int(byte_str, 2)
                        if 32 <= char_val <= 126:
                            flag_chars += chr(char_val)
                        else:
                            flag_chars += f"[{char_val}]"
                    except:
                        flag_chars += "?"
            
            print(f"Flag characters: '{flag_chars}'")
            
            # Look for the actual flag pattern
            if "tjctf{" in flag_chars:
                start = flag_chars.find("tjctf{")
                end = flag_chars.find("}", start) + 1
                if end > start:
                    final_flag = flag_chars[start:end]
                    print(f"\n*** FINAL FLAG: {final_flag} ***")
            else:
                print("Still no clean tjctf{ pattern found")
                
                # Try manual correction of first few bits
                print("Attempting manual bit correction...")
                
                # The 't' character should be 01110100
                # Let me see what I'm getting for the first character
                first_byte = flag_bits[:8]
                print(f"First byte: {first_byte} (should be 01110100 for 't')")
                
                # Try flipping the first bit
                corrected_bits = "0" + flag_bits[1:]
                corrected_chars = ""
                for i in range(0, min(128, len(corrected_bits)), 8):
                    byte_str = corrected_bits[i:i+8]
                    if len(byte_str) == 8:
                        try:
                            char_val = int(byte_str, 2)
                            if 32 <= char_val <= 126:
                                corrected_chars += chr(char_val)
                            else:
                                corrected_chars += f"[{char_val}]"
                        except:
                            corrected_chars += "?"
                
                print(f"Corrected characters: '{corrected_chars}'")
                
                if "tjctf{" in corrected_chars:
                    start = corrected_chars.find("tjctf{")
                    end = corrected_chars.find("}", start) + 1
                    if end > start:
                        final_flag = corrected_chars[start:end]
                        print(f"\n*** CORRECTED FLAG: {final_flag} ***")
