import numpy as np

# Fine-tune the threshold to get the correct first character

# Setup and solve
with open("encoded.txt", "r") as f:
    lines = f.readlines()

encoded = []
for line in lines:
    row = list(map(int, line.strip().split()))
    encoded.append(row)

encoded = np.array(encoded, dtype=np.float64)

filler = "In cybersecurity, a CTF (Capture The Flag) challenge is a competitive, gamified event where participants, either individually or in teams, are tasked with finding and exploiting vulnerabilities in systems to capture hidden information known as flags. These flags are typically used to score points. CTFs test skills in areas like cryptography, web security, reverse engineering, and forensics, offering an exciting way to learn, practice, and showcase cybersecurity expertise.  This flag is for you: "

filler_bin = "".join([bin(ord(c))[2:].zfill(8) for c in filler])
n = 64
filler_bin += "0" * (n - len(filler_bin) % n)
filler_matrix = np.array([list(map(int, list(filler_bin[i:i+n]))) for i in range(0, len(filler_bin), n)], dtype=np.float64)

A = filler_matrix
B = encoded[:63]

# Regularized solution
lambda_reg = 1e-6
AtA = A.T @ A
AtA_reg = AtA + lambda_reg * np.eye(64)
key_matrix = np.linalg.solve(AtA_reg, A.T @ B)

# Decode
key_inv = np.linalg.pinv(key_matrix)
decoded = encoded @ key_inv

# Try fine-tuned thresholds around 0.42
for thresh in [0.40, 0.41, 0.42, 0.43, 0.44]:
    print(f"\nThreshold {thresh}:")
    decoded_binary = (decoded > thresh).astype(int)
    
    text = ""
    for row in decoded_binary:
        for i in range(0, 64, 8):
            byte_bits = row[i:i+8]
            byte_str = "".join(map(str, byte_bits))
            if len(byte_str) == 8:
                try:
                    char_val = int(byte_str, 2)
                    if 32 <= char_val <= 126:
                        text += chr(char_val)
                    else:
                        text += '?'
                except:
                    text += '?'
    
    # Extract flag area
    if "This flag is for you:" in text:
        marker_pos = text.find("This flag is for you:")
        after_marker = text[marker_pos + len("This flag is for you:"):]
        flag_area = after_marker[:25]  # First 25 chars after marker
        print(f"  Flag area: '{flag_area}'")
        
        # Look for tjctf pattern
        if "tjctf{" in flag_area:
            start = flag_area.find("tjctf{")
            end = flag_area.find("}", start) + 1
            if end > start:
                potential_flag = flag_area[start:end]
                print(f"  *** FLAG: {potential_flag} ***")
                
                # Check if this looks meaningful
                flag_content = potential_flag[6:-1]  # Remove tjctf{ and }
                print(f"  Flag content: '{flag_content}'")
                
                # Check if it could be meaningful
                if any(word in flag_content.lower() for word in ['dot', 'matrix', 'linear', 'algebra', 'numpy']):
                    print(f"  ✓ Contains relevant keywords!")

# Let me also try a mixed approach - use different thresholds for different parts
print(f"\n" + "="*50)
print("Trying mixed threshold approach...")

# Use 0.42 for the first bit, 0.5 for the rest
decoded_mixed = decoded.copy()
decoded_mixed[63, 0] = 1 if decoded[63, 0] > 0.42 else 0  # First bit of flag
decoded_binary_mixed = (decoded_mixed > 0.5).astype(int)
decoded_binary_mixed[63, 0] = 1 if decoded[63, 0] > 0.42 else 0  # Override first bit

text_mixed = ""
for row in decoded_binary_mixed:
    for i in range(0, 64, 8):
        byte_bits = row[i:i+8]
        byte_str = "".join(map(str, byte_bits))
        if len(byte_str) == 8:
            try:
                char_val = int(byte_str, 2)
                if 32 <= char_val <= 126:
                    text_mixed += chr(char_val)
                else:
                    text_mixed += '?'
            except:
                text_mixed += '?'

if "This flag is for you:" in text_mixed:
    marker_pos = text_mixed.find("This flag is for you:")
    after_marker = text_mixed[marker_pos + len("This flag is for you:"):]
    flag_area = after_marker[:25]
    print(f"Mixed approach flag area: '{flag_area}'")
    
    if "tjctf{" in flag_area:
        start = flag_area.find("tjctf{")
        end = flag_area.find("}", start) + 1
        if end > start:
            mixed_flag = flag_area[start:end]
            print(f"*** MIXED APPROACH FLAG: {mixed_flag} ***")
