# TJCTF 2024 - Alchemist Reci<PERSON> Writeup

**Category:** Crypto  
**Points:** 331  
**Solves:** 59  
**Author:** 2027aliu

## Challenge Description

An alchemist claims to have a recipe to transform lead into gold. However, he accidentally encrypted it with a peculiar process of his own. He left behind his notes on the encryption method and an encrypted sample. Unfortunately, he spilled some magic ink on the notes, making them weirdly formatted. The notes include comments showing how he encrypted his recipe. Can you find his "golden" secret?

**Files provided:**
- `chall.py` - The encryption implementation
- `encrypted.txt` - The encrypted flag

## Initial Analysis

Looking at the challenge files, we have:

1. **chall.py**: Contains the encryption algorithm with obfuscated variable names
2. **encrypted.txt**: Contains the hex-encoded encrypted flag

The "weirdly formatted" notes refer to the intentionally obfuscated variable names in the Python code, making it harder to understand the algorithm at first glance.

## Code Analysis

### Constants and Key Material

```python
SNEEZE_FORK = "AurumPotabileEtChymicumSecretum"  # The encryption key
WUMBLE_BAG = 8  # Block size (8 bytes)
```

The key `"AurumPotabileEtChymicumSecretum"` is Latin meaning "Drinkable Gold and Chemical Secret" - fitting for an alchemist's recipe!

### Key Derivation Function: `glorbulate_sprockets_for_bamboozle()`

This function takes the key and derives three components using SHA256:

```python
def glorbulate_sprockets_for_bamboozle(blorbo):
    zing = {}
    yarp = hashlib.sha256(blorbo.encode()).digest()  # 32-byte hash
    zing['flibber'] = list(yarp[:WUMBLE_BAG])        # First 8 bytes
    zing['twizzle'] = list(yarp[WUMBLE_BAG:WUMBLE_BAG+16])  # Next 16 bytes
    glimbo = list(yarp[WUMBLE_BAG+16:])              # Remaining 8 bytes
    
    # Create substitution table using remaining bytes
    snorb = list(range(256))  # Identity permutation [0,1,2,...,255]
    sploop = 0
    for _ in range(256): 
        for z in glimbo:
            wob = (sploop + z) % 256
            snorb[sploop], snorb[wob] = snorb[wob], snorb[sploop]  # Swap
            sploop = (sploop + 1) % 256
    zing['drizzle'] = snorb
    return zing
```

**Key components:**
- `flibber`: First 8 bytes - used for final permutation ordering
- `twizzle`: Next 16 bytes - used for XOR operation
- `drizzle`: Substitution table created from remaining 8 bytes

### Block Encryption: `scrungle_crank()`

This function encrypts a single 8-byte block:

```python
def scrungle_crank(dingus, sprockets):
    # Step 1: Apply substitution using drizzle table
    zonked = bytes([sprockets['drizzle'][x] for x in dingus])
    
    # Step 2: XOR with twizzle (cycling through 16 bytes)
    quix = sprockets['twizzle']
    splatted = bytes([zonked[i] ^ quix[i % len(quix)] for i in range(WUMBLE_BAG)])
    
    # Step 3: Reorder bytes based on sorted flibber values
    wiggle = sprockets['flibber'] 
    waggly = sorted([(wiggle[i], i) for i in range(WUMBLE_BAG)])  # Sort by value
    zort = [oof for _, oof in waggly]  # Extract indices in sorted order
    plunk = [0] * WUMBLE_BAG
    for y in range(WUMBLE_BAG):
        x = zort[y]
        plunk[y] = splatted[x]  # Reorder according to sorted indices
    return bytes(plunk)
```

**Encryption steps per block:**
1. **Substitution**: Each byte is replaced using the `drizzle` lookup table
2. **XOR**: Result is XORed with `twizzle` bytes (cycling through 16 bytes)
3. **Permutation**: Bytes are reordered based on the sorted order of `flibber` values

### Full Encryption: `snizzle_bytegum()`

This function handles padding and processes the entire message:

```python
def snizzle_bytegum(bubbles, jellybean):
    # PKCS#7 style padding
    fuzz = WUMBLE_BAG - (len(bubbles) % WUMBLE_BAG)
    if fuzz == 0: 
        fuzz = WUMBLE_BAG
    bubbles += bytes([fuzz] * fuzz)
    
    # Process in 8-byte blocks
    glomp = b""
    for b in range(0, len(bubbles), WUMBLE_BAG):
        splinter = bubbles[b:b+WUMBLE_BAG]
        zap = scrungle_crank(splinter, jellybean)
        glomp += zap
    return glomp
```

## Decryption Strategy

To decrypt, we need to reverse each step of the encryption process in the correct order:

### 1. Reverse Block Decryption

For each 8-byte encrypted block, reverse the three steps:

```python
def reverse_scrungle_crank(encrypted_block, sprockets):
    # Step 1: Reverse the final reordering
    wiggle = sprockets['flibber']
    waggly = sorted([(wiggle[i], i) for i in range(WUMBLE_BAG)])
    zort = [oof for _, oof in waggly]
    
    splatted = [0] * WUMBLE_BAG
    for y in range(WUMBLE_BAG):
        splatted[zort[y]] = encrypted_block[y]  # Reverse: splatted[zort[y]] = plunk[y]
    splatted = bytes(splatted)

    # Step 2: Undo XOR with twizzle
    quix = sprockets['twizzle']
    zonked = bytes([splatted[i] ^ quix[i % len(quix)] for i in range(WUMBLE_BAG)])

    # Step 3: Reverse the drizzle substitution
    drizzle = sprockets['drizzle']
    reverse_drizzle = [0] * 256
    for i in range(256):
        reverse_drizzle[drizzle[i]] = i  # Create inverse lookup table

    original = bytes([reverse_drizzle[x] for x in zonked])
    return original
```

### 2. Full Decryption with Padding Removal

```python
def unsnizzle_bytegum(encrypted_data, jellybean):
    decrypted = b""
    for b in range(0, len(encrypted_data), WUMBLE_BAG):
        encrypted_block = encrypted_data[b:b+WUMBLE_BAG]
        decrypted_block = reverse_scrungle_crank(encrypted_block, jellybean)
        decrypted += decrypted_block
    
    # Remove PKCS#7 padding
    if decrypted:
        padding_length = decrypted[-1]
        if padding_length <= WUMBLE_BAG and all(b == padding_length for b in decrypted[-padding_length:]):
            decrypted = decrypted[:-padding_length]
    
    return decrypted
```

## Solution Implementation

The complete decryption script:

```python
def decrypt_recipe():
    # Read encrypted data
    with open("encrypted.txt", "r") as f:
        encrypted_hex = f.read().strip()
    
    encrypted_data = bytes.fromhex(encrypted_hex)
    
    # Generate the same key material used for encryption
    jellybean = glorbulate_sprockets_for_bamboozle(SNEEZE_FORK)
    
    # Decrypt
    decrypted = unsnizzle_bytegum(encrypted_data, jellybean)
    
    print(f"Decrypted recipe: {decrypted.decode()}")
    return decrypted
```

## Key Insights

1. **Obfuscation vs Security**: The challenge uses silly variable names to make the code harder to read, but the underlying algorithm is a straightforward custom block cipher.

2. **Reversible Operations**: Each step in the encryption is carefully designed to be reversible:
   - Substitution tables can be inverted
   - XOR is its own inverse
   - Permutations can be undone by tracking the reordering

3. **Block Cipher Structure**: This follows a classic block cipher pattern with substitution, XOR (like a stream cipher component), and permutation.

4. **Deterministic Key Derivation**: Using SHA256 ensures the same key always produces the same derived components.

## Running the Solution

```bash
python3 chall.py
```

Output:
```
Testing encryption/decryption:
Test data: b'tjctf{test_flag_12345678}'
Encrypted: b84254d7b5920901522eaf847ecb370771782ceb85cec9f2ad13c34943fe6553
Decrypted: b'tjctf{test_flag_12345678}'
Match: True

Decrypting the actual challenge:
Decrypted recipe: tjctf{thank_you_for_making_me_normal_again_yay}
```

## Flag

**`tjctf{thank_you_for_making_me_normal_again_yay}`**

The flag message humorously refers to "making me normal again" - likely referring to the process of un-obfuscating the weird variable names and understanding the algorithm!

## Lessons Learned

1. **Code Obfuscation**: Variable names can make code much harder to understand, but don't change the underlying logic
2. **Systematic Reversal**: Complex encryption can be broken down into reversible steps
3. **Testing**: Always test your decryption logic with known data before applying to the challenge
4. **Block Ciphers**: Understanding the structure helps identify how to reverse each component

This challenge was a great exercise in code analysis, algorithm reversal, and implementing the inverse of a custom encryption scheme.

## Complete Solution Code

Here's the full working decryption implementation added to the original `chall.py`:

```python
def reverse_scrungle_crank(encrypted_block, sprockets):
    """Reverse the scrungle_crank operation"""
    if len(encrypted_block) != WUMBLE_BAG:
        raise ValueError(f"Must be {WUMBLE_BAG} wumps for crankshaft.")

    # Step 1: Reverse the final reordering step (lines 28-34 in original)
    # The original does: plunk[y] = splatted[zort[y]]
    # So to reverse: splatted[zort[y]] = plunk[y]
    wiggle = sprockets['flibber']
    waggly = sorted([(wiggle[i], i) for i in range(WUMBLE_BAG)])
    zort = [oof for _, oof in waggly]

    splatted = [0] * WUMBLE_BAG
    for y in range(WUMBLE_BAG):
        splatted[zort[y]] = encrypted_block[y]
    splatted = bytes(splatted)

    # Step 2: Undo XOR with twizzle (line 27 in original)
    quix = sprockets['twizzle']
    zonked = bytes([splatted[i] ^ quix[i % len(quix)] for i in range(WUMBLE_BAG)])

    # Step 3: Reverse the drizzle permutation (line 25 in original)
    # The original does: zonked[i] = drizzle[dingus[i]]
    # So to reverse: dingus[i] = reverse_drizzle[zonked[i]]
    drizzle = sprockets['drizzle']
    reverse_drizzle = [0] * 256
    for i in range(256):
        reverse_drizzle[drizzle[i]] = i

    original = bytes([reverse_drizzle[x] for x in zonked])
    return original

def unsnizzle_bytegum(encrypted_data, jellybean):
    """Reverse the snizzle_bytegum operation"""
    if len(encrypted_data) % WUMBLE_BAG != 0:
        raise ValueError("Encrypted data length must be multiple of WUMBLE_BAG")

    decrypted = b""
    for b in range(0, len(encrypted_data), WUMBLE_BAG):
        encrypted_block = encrypted_data[b:b+WUMBLE_BAG]
        decrypted_block = reverse_scrungle_crank(encrypted_block, jellybean)
        decrypted += decrypted_block

    # Remove padding
    if decrypted:
        padding_length = decrypted[-1]
        if padding_length <= WUMBLE_BAG and all(b == padding_length for b in decrypted[-padding_length:]):
            decrypted = decrypted[:-padding_length]

    return decrypted

def decrypt_recipe():
    """Decrypt the recipe from encrypted.txt"""
    # Read encrypted data
    with open("encrypted.txt", "r") as f:
        encrypted_hex = f.read().strip()

    encrypted_data = bytes.fromhex(encrypted_hex)

    # Generate the same jellybean (key material) used for encryption
    jellybean = glorbulate_sprockets_for_bamboozle(SNEEZE_FORK)

    # Decrypt
    decrypted = unsnizzle_bytegum(encrypted_data, jellybean)

    print(f"Decrypted recipe: {decrypted.decode(errors='ignore')}")
    return decrypted

def test_encryption_decryption():
    """Test that our decryption correctly reverses encryption"""
    test_data = b"tjctf{test_flag_12345678}"  # Test with a known flag format

    # Encrypt
    jellybean = glorbulate_sprockets_for_bamboozle(SNEEZE_FORK)
    encrypted = snizzle_bytegum(test_data, jellybean)
    print(f"Test data: {test_data}")
    print(f"Encrypted: {encrypted.hex()}")

    # Decrypt
    decrypted = unsnizzle_bytegum(encrypted, jellybean)
    print(f"Decrypted: {decrypted}")
    print(f"Match: {test_data == decrypted}")

if __name__ == "__main__":
    # Test our decryption logic first
    print("Testing encryption/decryption:")
    test_encryption_decryption()
    print("\nDecrypting the actual challenge:")

    # Run decryption on the challenge
    decrypt_recipe()
```

## Technical Deep Dive

### Algorithm Classification

This cipher can be classified as a **custom block cipher** with the following characteristics:

- **Block size**: 8 bytes (64 bits)
- **Key size**: Variable (derived from SHA256 of input key)
- **Structure**: Substitution-Permutation Network (SPN) variant
- **Rounds**: Single round per block (relatively weak)

### Security Analysis

**Strengths:**
- Uses cryptographically strong SHA256 for key derivation
- Combines multiple operations (substitution, XOR, permutation)
- Non-linear substitution table generation

**Weaknesses:**
- Only one round of encryption per block
- Small block size (8 bytes) vulnerable to frequency analysis
- No diffusion between blocks (ECB-like mode)
- Deterministic key derivation allows precomputation attacks
- Custom crypto (never roll your own!)

### Cryptographic Operations Breakdown

1. **Substitution (S-box)**: The `drizzle` table acts as a substitution box, replacing each input byte with a different byte. This provides confusion.

2. **XOR with key stream**: The `twizzle` operation adds a stream cipher component, providing additional confusion.

3. **Permutation (P-box)**: The final reordering based on `flibber` provides diffusion within the block.

### Alternative Attack Vectors

While we solved this by reversing the algorithm, other potential approaches could include:

1. **Frequency Analysis**: With enough ciphertext, the 8-byte block size might reveal patterns
2. **Known Plaintext**: If we knew part of the flag format, we could derive key material
3. **Brute Force**: The key space is limited to SHA256 outputs, but still computationally infeasible

## Educational Value

This challenge excellently demonstrates:

1. **Reverse Engineering**: How to analyze obfuscated code systematically
2. **Cryptographic Primitives**: Understanding S-boxes, P-boxes, and key derivation
3. **Implementation Skills**: Converting mathematical operations to working code
4. **Testing Methodology**: Validating solutions with known test cases

The obfuscated variable names add a fun layer of difficulty without changing the core cryptographic challenge, making it an excellent educational exercise in both code analysis and cryptography.
