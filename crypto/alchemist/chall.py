import hashlib

SNEEZE_FORK = "AurumPotabileEtChymicumSecretum"
WUMBLE_BAG = 8 

def glorbulate_sprockets_for_bamboozle(blorbo):
    zing = {}
    yarp = hashlib.sha256(blorbo.encode()).digest() 
    zing['flibber'] = list(yarp[:WUMBLE_BAG])
    zing['twizzle'] = list(yarp[WUMBLE_BAG:WUMBLE_BAG+16])
    glimbo = list(yarp[WUMBLE_BAG+16:])
    snorb = list(range(256))
    sploop = 0
    for _ in range(256): 
        for z in glimbo:
            wob = (sploop + z) % 256
            snorb[sploop], snorb[wob] = snorb[wob], snorb[sploop]
            sploop = (sploop + 1) % 256
    zing['drizzle'] = snorb
    return zing

def scrungle_crank(dingus, sprockets):
    if len(dingus) != WUMBLE_BAG:
        raise ValueError(f"Must be {WUMBLE_BAG} wumps for crankshaft.")
    zonked = bytes([sprockets['drizzle'][x] for x in dingus])
    quix = sprockets['twizzle']
    splatted = bytes([zonked[i] ^ quix[i % len(quix)] for i in range(WUMBLE_BAG)])
    wiggle = sprockets['flibber'] 
    waggly = sorted([(wiggle[i], i) for i in range(WUMBLE_BAG)])
    zort = [oof for _, oof in waggly]
    plunk = [0] * WUMBLE_BAG
    for y in range(WUMBLE_BAG):
        x = zort[y]
        plunk[y] = splatted[x]
    return bytes(plunk)

def snizzle_bytegum(bubbles, jellybean):
    fuzz = WUMBLE_BAG - (len(bubbles) % WUMBLE_BAG)
    if fuzz == 0: 
        fuzz = WUMBLE_BAG
    bubbles += bytes([fuzz] * fuzz)
    glomp = b""
    for b in range(0, len(bubbles), WUMBLE_BAG):
        splinter = bubbles[b:b+WUMBLE_BAG]
        zap = scrungle_crank(splinter, jellybean)
        glomp += zap
    return glomp

def main():
    try:
        with open("flag.txt", "rb") as f:
            flag_content = f.read().strip()
    except FileNotFoundError:
        print("Error: flag.txt not found. Create it with the flag content.")
        return

    if not flag_content:
        print("Error: flag.txt is empty.")
        return

    print(f"Original Recipe (for generation only): {flag_content.decode(errors='ignore')}")

    jellybean = glorbulate_sprockets_for_bamboozle(SNEEZE_FORK)
    encrypted_recipe = snizzle_bytegum(flag_content, jellybean)

    with open("encrypted.txt", "w") as f_out:
        f_out.write(encrypted_recipe.hex())

    print(f"\nEncrypted recipe written to encrypted.txt:")
    print(encrypted_recipe.hex())

def reverse_scrungle_crank(encrypted_block, sprockets):
    """Reverse the scrungle_crank operation"""
    if len(encrypted_block) != WUMBLE_BAG:
        raise ValueError(f"Must be {WUMBLE_BAG} wumps for crankshaft.")

    # Step 1: Reverse the final reordering step (lines 28-34 in original)
    # The original does: plunk[y] = splatted[zort[y]]
    # So to reverse: splatted[zort[y]] = plunk[y]
    wiggle = sprockets['flibber']
    waggly = sorted([(wiggle[i], i) for i in range(WUMBLE_BAG)])
    zort = [oof for _, oof in waggly]

    splatted = [0] * WUMBLE_BAG
    for y in range(WUMBLE_BAG):
        splatted[zort[y]] = encrypted_block[y]
    splatted = bytes(splatted)

    # Step 2: Undo XOR with twizzle (line 27 in original)
    quix = sprockets['twizzle']
    zonked = bytes([splatted[i] ^ quix[i % len(quix)] for i in range(WUMBLE_BAG)])

    # Step 3: Reverse the drizzle permutation (line 25 in original)
    # The original does: zonked[i] = drizzle[dingus[i]]
    # So to reverse: dingus[i] = reverse_drizzle[zonked[i]]
    drizzle = sprockets['drizzle']
    reverse_drizzle = [0] * 256
    for i in range(256):
        reverse_drizzle[drizzle[i]] = i

    original = bytes([reverse_drizzle[x] for x in zonked])
    return original

def unsnizzle_bytegum(encrypted_data, jellybean):
    """Reverse the snizzle_bytegum operation"""
    if len(encrypted_data) % WUMBLE_BAG != 0:
        raise ValueError("Encrypted data length must be multiple of WUMBLE_BAG")

    decrypted = b""
    for b in range(0, len(encrypted_data), WUMBLE_BAG):
        encrypted_block = encrypted_data[b:b+WUMBLE_BAG]
        decrypted_block = reverse_scrungle_crank(encrypted_block, jellybean)
        decrypted += decrypted_block

    # Remove padding
    if decrypted:
        padding_length = decrypted[-1]
        if padding_length <= WUMBLE_BAG and all(b == padding_length for b in decrypted[-padding_length:]):
            decrypted = decrypted[:-padding_length]

    return decrypted

def decrypt_recipe():
    """Decrypt the recipe from encrypted.txt"""
    # Read encrypted data
    with open("encrypted.txt", "r") as f:
        encrypted_hex = f.read().strip()

    encrypted_data = bytes.fromhex(encrypted_hex)

    # Generate the same jellybean (key material) used for encryption
    jellybean = glorbulate_sprockets_for_bamboozle(SNEEZE_FORK)

    # Decrypt
    decrypted = unsnizzle_bytegum(encrypted_data, jellybean)

    print(f"Decrypted recipe: {decrypted.decode(errors='ignore')}")
    return decrypted

def test_encryption_decryption():
    """Test that our decryption correctly reverses encryption"""
    test_data = b"tjctf{test_flag_12345678}"  # Test with a known flag format

    # Encrypt
    jellybean = glorbulate_sprockets_for_bamboozle(SNEEZE_FORK)
    encrypted = snizzle_bytegum(test_data, jellybean)
    print(f"Test data: {test_data}")
    print(f"Encrypted: {encrypted.hex()}")

    # Decrypt
    decrypted = unsnizzle_bytegum(encrypted, jellybean)
    print(f"Decrypted: {decrypted}")
    print(f"Match: {test_data == decrypted}")

if __name__ == "__main__":
    # Test our decryption logic first
    print("Testing encryption/decryption:")
    test_encryption_decryption()
    print("\nDecrypting the actual challenge:")

    # Run decryption on the challenge
    decrypt_recipe()