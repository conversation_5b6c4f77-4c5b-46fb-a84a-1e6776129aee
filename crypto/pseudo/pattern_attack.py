#!/usr/bin/env python3
import random
import base64
import socket
import time

def get_key(username):
    """Replicate the key generation from server.py"""
    num_bits = 8 * len(username)
    rand = random.getrandbits(num_bits)
    rand_bits = bin(rand)[2:].zfill(num_bits)
    username_bits = ''.join([bin(ord(char))[2:].zfill(8) for char in username])
    xor_bits = ''.join([str(int(rand_bits[i]) ^ int(username_bits[i])) for i in range(num_bits)])
    xor_result = int(xor_bits, 2)
    shifted = ((xor_result << 3) & (1 << (num_bits + 3)) - 1) ^ 0x5A
    byte_data = shifted.to_bytes((shifted.bit_length() + 7) // 8, 'big')
    key = base64.b64encode(byte_data).decode('utf-8')
    return key

def reverse_key_to_rand(username, key):
    """Reverse the key generation to get the original random number"""
    try:
        # Decode base64
        byte_data = base64.b64decode(key)
        shifted = int.from_bytes(byte_data, 'big')
        
        # Reverse the shift and XOR with 0x5A
        num_bits = 8 * len(username)
        xor_result = (shifted ^ 0x5A) >> 3
        
        # Convert to binary
        xor_bits = bin(xor_result)[2:].zfill(num_bits)
        username_bits = ''.join([bin(ord(char))[2:].zfill(8) for char in username])
        
        # Reverse XOR to get original random bits
        rand_bits = ''.join([str(int(xor_bits[i]) ^ int(username_bits[i])) for i in range(num_bits)])
        rand = int(rand_bits, 2)
        
        return rand
    except Exception as e:
        return None

def main():
    print("Pattern-based attack")
    
    # Let's analyze the random outputs we got from the previous run
    # These were the outputs from creating accounts a, ab, abc, etc.
    observed_randoms = [
        (8, 22),
        (16, 35545), 
        (24, 6470004),
        (32, **********),
        (40, ************),
        (48, ***************),
        (56, *****************),
        (64, 6782985633583387648)
    ]
    
    print("Observed random values:")
    for bits, rand_val in observed_randoms:
        print(f"  {bits} bits: {rand_val}")
    
    # These random values were generated AFTER the admin accounts
    # So we need to work backwards
    
    # Let's try to find what seed could produce these values
    # We'll test seeds around the current time
    
    current_time = int(time.time())
    print(f"Current time: {current_time}")
    
    # Test seeds in a range around current time
    for time_offset in range(-7200, 0, 60):  # Go back 2 hours, every minute
        test_seed = current_time + time_offset
        
        random.seed(test_seed)
        
        # Skip the admin account generations (3 calls to getrandbits)
        # Admin001: 8*8 = 64 bits
        # Admin002: 8*8 = 64 bits  
        # Admin003: 8*8 = 64 bits
        random.getrandbits(64)  # Admin001
        random.getrandbits(64)  # Admin002
        random.getrandbits(64)  # Admin003
        
        # Now generate the test values and see if they match
        test_values = []
        test_usernames = ["a", "ab", "abc", "abcd", "test1", "test12", "test123", "test1234"]
        
        matches = True
        for i, username in enumerate(test_usernames):
            bits = len(username) * 8
            generated = random.getrandbits(bits)
            test_values.append(generated)
            
            if i < len(observed_randoms) and generated != observed_randoms[i][1]:
                matches = False
                break
        
        if matches:
            print(f"\nFound matching seed: {test_seed}")
            print(f"Time offset: {time_offset} seconds")
            
            # Now generate the admin keys with this seed
            random.seed(test_seed)
            
            admin_usernames = ["Admin001", "Admin002", "Admin003"]
            admin_keys = []
            
            for admin_user in admin_usernames:
                key = get_key(admin_user)
                admin_keys.append(key)
                print(f"Predicted key for {admin_user}: {key}")
            
            # Try to login with these keys
            try:
                s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                s.settimeout(10)
                s.connect(("tjc.tf", 31400))
                s.recv(4096)  # Welcome message
                
                flag_parts = []
                
                for i, admin_user in enumerate(admin_usernames):
                    try:
                        # Login
                        s.send(b"1\n")
                        time.sleep(0.1)
                        s.recv(4096)
                        
                        s.send(admin_user.encode() + b'\n')
                        time.sleep(0.1)
                        s.recv(4096)
                        
                        s.send(admin_keys[i].encode() + b'\n')
                        time.sleep(0.1)
                        response = s.recv(4096).decode()
                        
                        if "Logged in as" in response:
                            print(f"SUCCESS! Logged in as {admin_user}")
                            
                            # Get message
                            s.send(b"1\n")
                            time.sleep(0.1)
                            msg_response = s.recv(4096).decode()
                            
                            if "Your message:" in msg_response:
                                message = msg_response.split("Your message: ")[1].strip().split('\n')[0]
                                print(f"Flag part {i+1}: {message}")
                                flag_parts.append(message)
                            
                            # Logout
                            s.send(b"l\n")
                            time.sleep(0.1)
                            s.recv(4096)
                        else:
                            print(f"Failed to login as {admin_user}")
                            
                    except Exception as e:
                        print(f"Error with {admin_user}: {e}")
                
                s.close()
                
                if len(flag_parts) == 3:
                    print(f"\n🎉 FULL FLAG: {''.join(flag_parts)}")
                    return
                    
            except Exception as e:
                print(f"Connection error: {e}")
            
            return
    
    print("No matching seed found in the tested range")

if __name__ == "__main__":
    main()
