#!/usr/bin/env python3
import random
import base64
import socket
import time

def get_key(username):
    """Replicate the key generation from server.py"""
    num_bits = 8 * len(username)
    rand = random.getrandbits(num_bits)
    rand_bits = bin(rand)[2:].zfill(num_bits)
    username_bits = ''.join([bin(ord(char))[2:].zfill(8) for char in username])
    xor_bits = ''.join([str(int(rand_bits[i]) ^ int(username_bits[i])) for i in range(num_bits)])
    xor_result = int(xor_bits, 2)
    shifted = ((xor_result << 3) & (1 << (num_bits + 3)) - 1) ^ 0x5A
    byte_data = shifted.to_bytes((shifted.bit_length() + 7) // 8, 'big')
    key = base64.b64encode(byte_data).decode('utf-8')
    return key

def reverse_key_to_rand(username, key):
    """Reverse the key generation to get the original random number"""
    try:
        byte_data = base64.b64decode(key)
        shifted = int.from_bytes(byte_data, 'big')
        num_bits = 8 * len(username)
        xor_result = (shifted ^ 0x5A) >> 3
        xor_bits = bin(xor_result)[2:].zfill(num_bits)
        username_bits = ''.join([bin(ord(char))[2:].zfill(8) for char in username])
        rand_bits = ''.join([str(int(xor_bits[i]) ^ int(username_bits[i])) for i in range(num_bits)])
        rand = int(rand_bits, 2)
        return rand
    except:
        return None

def create_account_and_get_key(s, username):
    """Create an account and extract the key"""
    try:
        s.send(b"2\n")
        time.sleep(0.1)
        s.recv(4096)
        
        s.send(username.encode() + b'\n')
        time.sleep(0.1)
        response = s.recv(4096).decode()
        
        if "Your sign-in key is:" in response:
            for line in response.split('\n'):
                if "Your sign-in key is:" in line:
                    key = line.split("Your sign-in key is: ")[1].strip()
                    return key
        return None
    except:
        return None

def simple_mt_predict(observed_outputs):
    """
    Simple approach to predict previous MT outputs
    This is a simplified version - in practice, you'd need a full MT state recovery
    """
    # For this CTF, maybe we can try a simpler approach
    # Let's see if we can find a pattern or use brute force on a smaller space
    
    # The key insight: if we know some outputs, we might be able to 
    # brute force the seed that would produce those outputs
    
    current_time = int(time.time())
    
    # Try seeds in a reasonable range around current time
    for time_offset in range(-86400, 86400, 60):  # ±1 day, every minute
        test_seed = current_time + time_offset
        
        random.seed(test_seed)
        
        # Skip the admin generations (3 calls to getrandbits(64))
        random.getrandbits(64)  # Admin001
        random.getrandbits(64)  # Admin002  
        random.getrandbits(64)  # Admin003
        
        # Check if this produces our observed outputs
        matches = True
        for bits, expected_output in observed_outputs:
            actual_output = random.getrandbits(bits)
            if actual_output != expected_output:
                matches = False
                break
        
        if matches:
            return test_seed
    
    return None

def main():
    print("Mersenne Twister Recovery Attack")
    
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        s.settimeout(30)
        s.connect(("tjc.tf", 31400))
        s.recv(4096)  # Welcome message
        
        print("Creating accounts to observe random outputs...")
        
        # Create accounts with specific lengths to get different bit sizes
        test_accounts = [
            ("x", 8),      # 8 bits
            ("xy", 16),    # 16 bits
            ("xyz", 24),   # 24 bits
            ("xyzw", 32),  # 32 bits
        ]
        
        observed_outputs = []
        
        for username, expected_bits in test_accounts:
            key = create_account_and_get_key(s, username)
            if key:
                print(f"Created {username} with key: {key}")
                rand_val = reverse_key_to_rand(username, key)
                if rand_val is not None:
                    print(f"  Extracted {expected_bits}-bit random: {rand_val}")
                    observed_outputs.append((expected_bits, rand_val))
            else:
                print(f"Failed to create account for {username}")
        
        s.close()
        
        if len(observed_outputs) < 3:
            print("Not enough observed outputs for attack")
            return
        
        print(f"\nAttempting to find seed that produces these outputs...")
        print("This may take a while...")
        
        seed = simple_mt_predict(observed_outputs)
        
        if seed is not None:
            print(f"Found seed: {seed}")
            
            # Generate admin keys with this seed
            random.seed(seed)
            
            admin_usernames = ["Admin001", "Admin002", "Admin003"]
            admin_keys = []
            
            for admin_user in admin_usernames:
                key = get_key(admin_user)
                admin_keys.append(key)
                print(f"Predicted key for {admin_user}: {key}")
            
            # Try to login
            s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            s.connect(("tjc.tf", 31400))
            s.recv(4096)
            
            flag_parts = []
            
            for i, admin_user in enumerate(admin_usernames):
                try:
                    s.send(b"1\n")
                    time.sleep(0.1)
                    s.recv(4096)
                    
                    s.send(admin_user.encode() + b'\n')
                    time.sleep(0.1)
                    s.recv(4096)
                    
                    s.send(admin_keys[i].encode() + b'\n')
                    time.sleep(0.1)
                    response = s.recv(4096).decode()
                    
                    if "Logged in as" in response:
                        print(f"SUCCESS! Logged in as {admin_user}")
                        
                        s.send(b"1\n")
                        time.sleep(0.1)
                        msg_response = s.recv(4096).decode()
                        
                        if "Your message:" in msg_response:
                            message = msg_response.split("Your message: ")[1].strip().split('\n')[0]
                            print(f"Flag part {i+1}: {message}")
                            flag_parts.append(message)
                        
                        s.send(b"l\n")
                        time.sleep(0.1)
                        s.recv(4096)
                    else:
                        print(f"Failed to login as {admin_user}")
                        
                except Exception as e:
                    print(f"Error with {admin_user}: {e}")
            
            s.close()
            
            if len(flag_parts) == 3:
                print(f"\n🎉 FULL FLAG: {''.join(flag_parts)}")
            else:
                print(f"Only got {len(flag_parts)} flag parts")
        else:
            print("Could not find matching seed")
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
