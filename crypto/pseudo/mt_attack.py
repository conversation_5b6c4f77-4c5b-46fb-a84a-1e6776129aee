#!/usr/bin/env python3
import random
import base64
import socket
import time

def get_key(username):
    """Replicate the key generation from server.py"""
    num_bits = 8 * len(username)
    rand = random.getrandbits(num_bits)
    rand_bits = bin(rand)[2:].zfill(num_bits)
    username_bits = ''.join([bin(ord(char))[2:].zfill(8) for char in username])
    xor_bits = ''.join([str(int(rand_bits[i]) ^ int(username_bits[i])) for i in range(num_bits)])
    xor_result = int(xor_bits, 2)
    shifted = ((xor_result << 3) & (1 << (num_bits + 3)) - 1) ^ 0x5A
    byte_data = shifted.to_bytes((shifted.bit_length() + 7) // 8, 'big')
    key = base64.b64encode(byte_data).decode('utf-8')
    return key

def reverse_key_to_rand(username, key):
    """Reverse the key generation to get the original random number"""
    try:
        # Decode base64
        byte_data = base64.b64decode(key)
        shifted = int.from_bytes(byte_data, 'big')
        
        # Reverse the shift and XOR with 0x5A
        num_bits = 8 * len(username)
        xor_result = (shifted ^ 0x5A) >> 3
        
        # Convert to binary
        xor_bits = bin(xor_result)[2:].zfill(num_bits)
        username_bits = ''.join([bin(ord(char))[2:].zfill(8) for char in username])
        
        # Reverse XOR to get original random bits
        rand_bits = ''.join([str(int(xor_bits[i]) ^ int(username_bits[i])) for i in range(num_bits)])
        rand = int(rand_bits, 2)
        
        return rand
    except Exception as e:
        print(f"Error reversing key: {e}")
        return None

def create_account_and_get_key(s, username):
    """Create an account and extract the key"""
    try:
        # Send option 2 (Create Account)
        s.send(b"2\n")
        time.sleep(0.2)
        response = s.recv(4096).decode()
        
        # Send username
        s.send(username.encode() + b'\n')
        time.sleep(0.2)
        response = s.recv(4096).decode()
        
        # Extract key from response
        if "Your sign-in key is:" in response:
            lines = response.split('\n')
            for line in lines:
                if "Your sign-in key is:" in line:
                    key = line.split("Your sign-in key is: ")[1].strip()
                    return key
        
        return None
    except Exception as e:
        print(f"Error creating account: {e}")
        return None

def main():
    print("Mersenne Twister State Recovery Attack")
    
    try:
        # Connect to server
        s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        s.settimeout(30)
        s.connect(("tjc.tf", 31400))
        
        # Receive welcome message
        welcome = s.recv(4096).decode()
        print("Connected to server")
        
        # Create multiple accounts to gather random outputs
        # We need accounts with known usernames to reverse the random values
        test_usernames = [
            "a",        # 1 byte = 8 bits
            "ab",       # 2 bytes = 16 bits  
            "abc",      # 3 bytes = 24 bits
            "abcd",     # 4 bytes = 32 bits
            "test1",    # 5 bytes = 40 bits
            "test12",   # 6 bytes = 48 bits
            "test123",  # 7 bytes = 56 bits
            "test1234", # 8 bytes = 64 bits
        ]
        
        print("Creating test accounts to gather random outputs...")
        random_outputs = []
        
        for username in test_usernames:
            key = create_account_and_get_key(s, username)
            if key:
                print(f"Created {username} with key: {key}")
                rand_val = reverse_key_to_rand(username, key)
                if rand_val is not None:
                    print(f"  Extracted random: {rand_val} ({len(username)*8} bits)")
                    random_outputs.append((len(username)*8, rand_val))
            else:
                print(f"Failed to create account for {username}")
        
        print(f"\nGathered {len(random_outputs)} random outputs")
        
        # Now we need to work backwards to predict the admin keys
        # The admin accounts were created first, so we need to "rewind" the state
        
        # For now, let's try a simpler approach - maybe the server restarts frequently
        # and we can predict based on timing
        
        print("\nTrying to predict admin keys based on gathered data...")
        
        # Try to find a pattern or use the gathered data to predict
        # This is a simplified approach - in a real attack, we'd need more sophisticated
        # Mersenne Twister state recovery
        
        admin_usernames = ["Admin001", "Admin002", "Admin003"]
        
        # Let's try to brute force with a smaller range based on what we learned
        for offset in range(-100, 100):
            # Try to simulate the state at server startup
            test_seed = int(time.time()) + offset
            random.seed(test_seed)
            
            # Generate admin keys
            admin_keys = []
            for admin_user in admin_usernames:
                key = get_key(admin_user)
                admin_keys.append(key)
            
            # Test first admin account
            try:
                # Send option 1 (Sign-In)
                s.send(b"1\n")
                time.sleep(0.1)
                s.recv(4096)
                
                # Send username
                s.send(admin_usernames[0].encode() + b'\n')
                time.sleep(0.1)
                s.recv(4096)
                
                # Send key
                s.send(admin_keys[0].encode() + b'\n')
                time.sleep(0.1)
                response = s.recv(4096).decode()
                
                if "Logged in as" in response:
                    print(f"SUCCESS! Found working seed: {test_seed}")
                    print(f"Admin001 key: {admin_keys[0]}")
                    
                    # Get the flag part
                    s.send(b"1\n")
                    time.sleep(0.1)
                    msg_response = s.recv(4096).decode()
                    
                    if "Your message:" in msg_response:
                        message = msg_response.split("Your message: ")[1].strip().split('\n')[0]
                        print(f"Flag part 1: {message}")
                    
                    # Logout and try other admins
                    s.send(b"l\n")
                    time.sleep(0.1)
                    s.recv(4096)
                    
                    # Try other admin accounts
                    flag_parts = [message] if 'message' in locals() else []
                    
                    for i in range(1, 3):
                        try:
                            s.send(b"1\n")
                            time.sleep(0.1)
                            s.recv(4096)
                            
                            s.send(admin_usernames[i].encode() + b'\n')
                            time.sleep(0.1)
                            s.recv(4096)
                            
                            s.send(admin_keys[i].encode() + b'\n')
                            time.sleep(0.1)
                            response = s.recv(4096).decode()
                            
                            if "Logged in as" in response:
                                s.send(b"1\n")
                                time.sleep(0.1)
                                msg_response = s.recv(4096).decode()
                                
                                if "Your message:" in msg_response:
                                    message = msg_response.split("Your message: ")[1].strip().split('\n')[0]
                                    print(f"Flag part {i+1}: {message}")
                                    flag_parts.append(message)
                                
                                s.send(b"l\n")
                                time.sleep(0.1)
                                s.recv(4096)
                        except:
                            pass
                    
                    if len(flag_parts) == 3:
                        print(f"\n🎉 FULL FLAG: {''.join(flag_parts)}")
                        s.close()
                        return
                    
                    break
                    
            except Exception as e:
                continue
        
        s.close()
        print("Failed to find the correct seed")
        
    except Exception as e:
        print(f"Connection error: {e}")

if __name__ == "__main__":
    main()
