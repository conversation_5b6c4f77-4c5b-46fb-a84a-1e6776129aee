#!/usr/bin/env python3
import random
import base64
import socket
import time

def get_key(username):
    """Replicate the key generation from server.py"""
    num_bits = 8 * len(username)
    rand = random.getrandbits(num_bits)
    rand_bits = bin(rand)[2:].zfill(num_bits)
    username_bits = ''.join([bin(ord(char))[2:].zfill(8) for char in username])
    xor_bits = ''.join([str(int(rand_bits[i]) ^ int(username_bits[i])) for i in range(num_bits)])
    xor_result = int(xor_bits, 2)
    shifted = ((xor_result << 3) & (1 << (num_bits + 3)) - 1) ^ 0x5A
    byte_data = shifted.to_bytes((shifted.bit_length() + 7) // 8, 'big')
    key = base64.b64encode(byte_data).decode('utf-8')
    return key

def test_simple_seeds():
    """Test very simple seeds systematically"""
    print("Testing simple seeds...")
    
    # Test seeds 0-1000 one by one
    for seed in range(1000):
        if seed % 100 == 0:
            print(f"Testing seed {seed}...")
        
        random.seed(seed)
        
        # Generate admin keys
        admin_keys = []
        for admin_user in ["Admin001", "Admin002", "Admin003"]:
            key = get_key(admin_user)
            admin_keys.append(key)
        
        # Test Admin001 quickly
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            s.settimeout(3)
            s.connect(("tjc.tf", 31400))
            s.recv(4096)
            
            s.send(b"1\n")
            time.sleep(0.05)
            s.recv(4096)
            
            s.send(b"Admin001\n")
            time.sleep(0.05)
            s.recv(4096)
            
            s.send(admin_keys[0].encode() + b'\n')
            time.sleep(0.05)
            response = s.recv(4096).decode()
            
            s.close()
            
            if "Logged in as" in response:
                print(f"\n🎉 FOUND WORKING SEED: {seed}")
                print(f"Admin keys: {admin_keys}")
                
                # Get all flag parts
                flag_parts = []
                for i, admin_user in enumerate(["Admin001", "Admin002", "Admin003"]):
                    try:
                        s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                        s.connect(("tjc.tf", 31400))
                        s.recv(4096)
                        
                        s.send(b"1\n")
                        time.sleep(0.1)
                        s.recv(4096)
                        
                        s.send(admin_user.encode() + b'\n')
                        time.sleep(0.1)
                        s.recv(4096)
                        
                        s.send(admin_keys[i].encode() + b'\n')
                        time.sleep(0.1)
                        response = s.recv(4096).decode()
                        
                        if "Logged in as" in response:
                            s.send(b"1\n")
                            time.sleep(0.1)
                            msg_response = s.recv(4096).decode()
                            
                            if "Your message:" in msg_response:
                                message = msg_response.split("Your message: ")[1].strip().split('\n')[0]
                                print(f"Flag part {i+1}: {message}")
                                flag_parts.append(message)
                        
                        s.close()
                        
                    except Exception as e:
                        print(f"Error getting flag part {i+1}: {e}")
                
                if len(flag_parts) == 3:
                    print(f"\n🎉 FULL FLAG: {''.join(flag_parts)}")
                
                return seed
                
        except Exception as e:
            continue
    
    return None

def main():
    print("Simple systematic test")
    
    # First, let's see if the server is deterministic
    print("Testing if server is deterministic...")
    
    keys_observed = []
    for i in range(5):
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            s.settimeout(5)
            s.connect(("tjc.tf", 31400))
            s.recv(4096)
            
            s.send(b"2\n")
            time.sleep(0.1)
            s.recv(4096)
            
            s.send(f"test{i}\n".encode())
            time.sleep(0.1)
            response = s.recv(4096).decode()
            
            if "Your sign-in key is:" in response:
                key = response.split("Your sign-in key is: ")[1].strip().split('\n')[0]
                keys_observed.append(key)
                print(f"Test {i+1}: {key}")
            
            s.close()
            time.sleep(1)
            
        except Exception as e:
            print(f"Error in test {i+1}: {e}")
    
    print(f"\nObserved {len(keys_observed)} keys")
    if len(set(keys_observed)) == 1:
        print("All keys are the same! Server might be using a fixed seed.")
    elif len(set(keys_observed)) < len(keys_observed):
        print("Some keys repeat. Server might restart frequently.")
    else:
        print("All keys are different. Server uses different random state each time.")
    
    # Now try the systematic seed testing
    result = test_simple_seeds()
    
    if result is None:
        print("No working seed found in range 0-999")
    else:
        print(f"Success with seed {result}!")

if __name__ == "__main__":
    main()
