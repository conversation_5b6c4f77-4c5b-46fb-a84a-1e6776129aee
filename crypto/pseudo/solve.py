#!/usr/bin/env python3
import random
import base64
import socket
import time

def get_key(username, rand_state=None):
    """Replicate the key generation from server.py"""
    if rand_state is not None:
        random.setstate(rand_state)
    
    num_bits = 8 * len(username)
    rand = random.getrandbits(num_bits)
    rand_bits = bin(rand)[2:].zfill(num_bits)
    username_bits = ''.join([bin(ord(char))[2:].zfill(8) for char in username])
    xor_bits = ''.join([str(int(rand_bits[i]) ^ int(username_bits[i])) for i in range(num_bits)])
    xor_result = int(xor_bits, 2)
    shifted = ((xor_result << 3) & (1 << (num_bits + 3)) - 1) ^ 0x5A
    byte_data = shifted.to_bytes((shifted.bit_length() + 7) // 8, 'big')
    key = base64.b64encode(byte_data).decode('utf-8')
    return key

def reverse_key_to_rand(username, key):
    """Reverse the key generation to get the original random number"""
    try:
        # Decode base64
        byte_data = base64.b64decode(key)
        shifted = int.from_bytes(byte_data, 'big')
        
        # Reverse the shift and XOR with 0x5A
        num_bits = 8 * len(username)
        xor_result = (shifted ^ 0x5A) >> 3
        
        # Convert to binary
        xor_bits = bin(xor_result)[2:].zfill(num_bits)
        username_bits = ''.join([bin(ord(char))[2:].zfill(8) for char in username])
        
        # Reverse XOR to get original random bits
        rand_bits = ''.join([str(int(xor_bits[i]) ^ int(username_bits[i])) for i in range(num_bits)])
        rand = int(rand_bits, 2)
        
        return rand
    except:
        return None

def connect_to_server():
    """Connect to the challenge server"""
    host = "tjc.tf"
    port = 31400
    
    s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    s.connect((host, port))
    return s

def send_and_receive(s, data):
    """Send data and receive response"""
    s.send(data.encode() + b'\n')
    time.sleep(0.1)
    response = s.recv(4096).decode()
    return response

def create_account(s, username):
    """Create an account and get the key"""
    # Send option 2 (Create Account)
    send_and_receive(s, "2")
    
    # Send username
    response = send_and_receive(s, username)
    
    # Extract key from response
    if "Your sign-in key is:" in response:
        key = response.split("Your sign-in key is: ")[1].strip().split('\n')[0]
        return key
    return None

def login(s, username, key):
    """Login with username and key"""
    # Send option 1 (Sign-In)
    send_and_receive(s, "1")
    
    # Send username
    send_and_receive(s, username)
    
    # Send key
    response = send_and_receive(s, key)
    
    return "Logged in as" in response

def view_message(s):
    """View the current user's message"""
    # Send option 1 (View Message)
    response = send_and_receive(s, "1")
    
    if "Your message:" in response:
        message = response.split("Your message: ")[1].strip().split('\n')[0]
        return message
    return None

def logout(s):
    """Logout from current session"""
    send_and_receive(s, "l")

def main():
    print("Starting CTF solve...")

    # The key insight: Python's random module uses a default seed if none is provided
    # When the server starts, it likely uses the default random state
    # Let's try to predict the admin keys by simulating the server startup

    print("Attempting to predict admin keys...")

    # Try different seeds, including the default behavior
    admin_usernames = ["Admin001", "Admin002", "Admin003"]
    flag_parts = []

    # First, let's try with no explicit seed (default behavior)
    print("Trying default random state...")
    random.seed()  # This uses system time, but let's try some common patterns

    # Try some common seeds
    common_seeds = [None, 0, 1, 42, 1337, 12345, int(time.time())]

    for seed in common_seeds:
        print(f"\nTrying seed: {seed}")

        if seed is not None:
            random.seed(seed)
        else:
            random.seed()

        # Simulate the server creating admin accounts
        admin_keys = []
        for admin_user in admin_usernames:
            key = get_key(admin_user)
            admin_keys.append(key)
            print(f"  Predicted key for {admin_user}: {key}")

        # Try to connect and login with these keys
        try:
            s = connect_to_server()
            s.recv(4096)  # Receive welcome message

            success_count = 0
            for i, admin_user in enumerate(admin_usernames):
                try:
                    if login(s, admin_user, admin_keys[i]):
                        print(f"SUCCESS! Logged in as {admin_user}")
                        message = view_message(s)
                        if message:
                            print(f"Flag part {i+1}: {message}")
                            flag_parts.append(message)
                        logout(s)
                        success_count += 1
                except Exception as e:
                    print(f"Failed to login as {admin_user}: {e}")

            s.close()

            if success_count == 3:
                print(f"\nFull flag: {''.join(flag_parts)}")
                return

        except Exception as e:
            print(f"Connection error: {e}")

    # If simple seeds don't work, try a more sophisticated approach
    print("\nTrying time-based seeds around current time...")
    current_time = int(time.time())

    # Try seeds around current time (server might have started recently)
    for offset in range(-3600, 3600, 60):  # Try every minute in a 2-hour window
        seed = current_time + offset

        random.seed(seed)

        # Simulate admin account creation
        admin_keys = [get_key(admin_user) for admin_user in admin_usernames]

        try:
            s = connect_to_server()
            s.recv(4096)  # Receive welcome message

            # Try first admin account to see if we're on the right track
            if login(s, admin_usernames[0], admin_keys[0]):
                print(f"Found working seed: {seed}")
                message = view_message(s)
                if message:
                    print(f"Flag part 1: {message}")
                    flag_parts = [message]
                logout(s)

                # Try other admin accounts
                for i in range(1, 3):
                    if login(s, admin_usernames[i], admin_keys[i]):
                        message = view_message(s)
                        if message:
                            print(f"Flag part {i+1}: {message}")
                            flag_parts.append(message)
                        logout(s)

                s.close()

                if len(flag_parts) == 3:
                    print(f"\nFull flag: {''.join(flag_parts)}")
                    return

            s.close()

        except Exception as e:
            continue

if __name__ == "__main__":
    main()
