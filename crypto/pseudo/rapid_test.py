#!/usr/bin/env python3
import random
import base64
import socket
import time
import threading

def get_key(username):
    """Replicate the key generation from server.py"""
    num_bits = 8 * len(username)
    rand = random.getrandbits(num_bits)
    rand_bits = bin(rand)[2:].zfill(num_bits)
    username_bits = ''.join([bin(ord(char))[2:].zfill(8) for char in username])
    xor_bits = ''.join([str(int(rand_bits[i]) ^ int(username_bits[i])) for i in range(num_bits)])
    xor_result = int(xor_bits, 2)
    shifted = ((xor_result << 3) & (1 << (num_bits + 3)) - 1) ^ 0x5A
    byte_data = shifted.to_bytes((shifted.bit_length() + 7) // 8, 'big')
    key = base64.b64encode(byte_data).decode('utf-8')
    return key

def test_connection():
    """Test a single connection and try to create an account"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        s.settimeout(3)
        s.connect(("tjc.tf", 31400))
        s.recv(4096)  # Welcome message
        
        # Create account with username "test"
        s.send(b"2\n")
        time.sleep(0.05)
        s.recv(4096)
        
        s.send(b"test\n")
        time.sleep(0.05)
        response = s.recv(4096).decode()
        
        s.close()
        
        if "Your sign-in key is:" in response:
            key = response.split("Your sign-in key is: ")[1].strip().split('\n')[0]
            return key
        
        return None
        
    except Exception as e:
        return None

def main():
    print("Rapid testing to see if server restarts frequently...")
    
    # Test multiple connections rapidly
    keys = []
    for i in range(20):
        key = test_connection()
        if key:
            keys.append(key)
            print(f"Connection {i+1}: {key}")
        else:
            print(f"Connection {i+1}: Failed")
        
        time.sleep(0.5)  # Small delay between connections
    
    print(f"\nGot {len(keys)} keys")
    
    # Check if any keys repeat (indicating server restart)
    unique_keys = set(keys)
    print(f"Unique keys: {len(unique_keys)}")
    
    if len(unique_keys) < len(keys):
        print("Found repeated keys! Server might be restarting.")
        
        # Find the repeated key and try to predict admin keys
        for key in unique_keys:
            if keys.count(key) > 1:
                print(f"Key {key} appeared {keys.count(key)} times")
                
                # This suggests the server restarted and used the same seed
                # Let's try to find what seed produces this key for username "test"
                for seed in range(10000):
                    random.seed(seed)
                    # Skip admin accounts
                    random.getrandbits(64)  # Admin001
                    random.getrandbits(64)  # Admin002
                    random.getrandbits(64)  # Admin003
                    
                    predicted_key = get_key("test")
                    if predicted_key == key:
                        print(f"Found seed {seed} that produces key {key}")
                        
                        # Generate admin keys with this seed
                        random.seed(seed)
                        admin_keys = []
                        for admin_user in ["Admin001", "Admin002", "Admin003"]:
                            admin_key = get_key(admin_user)
                            admin_keys.append(admin_key)
                            print(f"Predicted key for {admin_user}: {admin_key}")
                        
                        # Try to login with these keys
                        try:
                            s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                            s.connect(("tjc.tf", 31400))
                            s.recv(4096)
                            
                            flag_parts = []
                            
                            for i, admin_user in enumerate(["Admin001", "Admin002", "Admin003"]):
                                try:
                                    s.send(b"1\n")
                                    time.sleep(0.1)
                                    s.recv(4096)
                                    
                                    s.send(admin_user.encode() + b'\n')
                                    time.sleep(0.1)
                                    s.recv(4096)
                                    
                                    s.send(admin_keys[i].encode() + b'\n')
                                    time.sleep(0.1)
                                    response = s.recv(4096).decode()
                                    
                                    if "Logged in as" in response:
                                        print(f"SUCCESS! Logged in as {admin_user}")
                                        
                                        s.send(b"1\n")
                                        time.sleep(0.1)
                                        msg_response = s.recv(4096).decode()
                                        
                                        if "Your message:" in msg_response:
                                            message = msg_response.split("Your message: ")[1].strip().split('\n')[0]
                                            print(f"Flag part {i+1}: {message}")
                                            flag_parts.append(message)
                                        
                                        s.send(b"l\n")
                                        time.sleep(0.1)
                                        s.recv(4096)
                                    else:
                                        print(f"Failed to login as {admin_user}")
                                        
                                except Exception as e:
                                    print(f"Error with {admin_user}: {e}")
                            
                            s.close()
                            
                            if len(flag_parts) == 3:
                                print(f"\n🎉 FULL FLAG: {''.join(flag_parts)}")
                                return
                                
                        except Exception as e:
                            print(f"Error testing admin keys: {e}")
                        
                        break
                break
    else:
        print("All keys are unique. Server doesn't seem to restart frequently.")
        print("Keys observed:")
        for i, key in enumerate(keys):
            print(f"  {i+1}: {key}")

if __name__ == "__main__":
    main()
