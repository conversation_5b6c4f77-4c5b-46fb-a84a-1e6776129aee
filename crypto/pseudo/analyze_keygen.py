#!/usr/bin/env python3
import random
import base64

def get_key(username):
    """Replicate the key generation from server.py"""
    num_bits = 8 * len(username)
    rand = random.getrandbits(num_bits)
    print(f"Random value: {rand} (binary: {bin(rand)})")
    
    rand_bits = bin(rand)[2:].zfill(num_bits)
    print(f"Random bits: {rand_bits}")
    
    username_bits = ''.join([bin(ord(char))[2:].zfill(8) for char in username])
    print(f"Username bits: {username_bits}")
    
    xor_bits = ''.join([str(int(rand_bits[i]) ^ int(username_bits[i])) for i in range(num_bits)])
    print(f"XOR result bits: {xor_bits}")
    
    xor_result = int(xor_bits, 2)
    print(f"XOR result: {xor_result}")
    
    shifted = ((xor_result << 3) & (1 << (num_bits + 3)) - 1) ^ 0x5A
    print(f"After shift and XOR with 0x5A: {shifted}")
    
    byte_data = shifted.to_bytes((shifted.bit_length() + 7) // 8, 'big')
    print(f"Byte data: {byte_data}")
    
    key = base64.b64encode(byte_data).decode('utf-8')
    print(f"Final key: {key}")
    
    return key

def analyze_pattern():
    """Analyze the key generation pattern"""
    print("Analyzing key generation pattern...")
    
    # Test with different usernames
    test_usernames = ["A", "AA", "AAA", "AAAA", "Admin001"]
    
    for username in test_usernames:
        print(f"\n--- Testing username: {username} ---")
        random.seed(42)  # Use fixed seed for analysis
        key = get_key(username)
        print()

def reverse_analysis():
    """Try to understand if there are any weaknesses in the key generation"""
    print("Reverse analysis...")
    
    # What if we try usernames that might produce predictable patterns?
    # For example, usernames with all zeros in binary
    
    # Character with ASCII 0 doesn't exist, but let's try other patterns
    # What about usernames that might cause issues with the bit operations?
    
    test_cases = [
        "\x00",  # Null character (if allowed)
        "\x01",  # Character with mostly zeros
        "\x7f",  # Character with many ones
        "\xff",  # All ones (if allowed)
    ]
    
    for char in test_cases:
        try:
            print(f"\n--- Testing character: {repr(char)} (ASCII {ord(char)}) ---")
            random.seed(42)
            key = get_key(char)
        except Exception as e:
            print(f"Error with character {repr(char)}: {e}")

def main():
    analyze_pattern()
    print("\n" + "="*50 + "\n")
    reverse_analysis()

if __name__ == "__main__":
    main()
