#!/usr/bin/env python3
import random
import base64
import socket
import time

def get_key(username):
    """Replicate the key generation from server.py"""
    num_bits = 8 * len(username)
    rand = random.getrandbits(num_bits)
    rand_bits = bin(rand)[2:].zfill(num_bits)
    username_bits = ''.join([bin(ord(char))[2:].zfill(8) for char in username])
    xor_bits = ''.join([str(int(rand_bits[i]) ^ int(username_bits[i])) for i in range(num_bits)])
    xor_result = int(xor_bits, 2)
    shifted = ((xor_result << 3) & (1 << (num_bits + 3)) - 1) ^ 0x5A
    byte_data = shifted.to_bytes((shifted.bit_length() + 7) // 8, 'big')
    key = base64.b64encode(byte_data).decode('utf-8')
    return key

def test_login(admin_user, admin_key):
    """Test login with a specific admin user and key"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        s.settimeout(5)
        s.connect(("tjc.tf", 31400))
        s.recv(4096)  # Welcome message
        
        # Login
        s.send(b"1\n")
        time.sleep(0.1)
        s.recv(4096)
        
        s.send(admin_user.encode() + b'\n')
        time.sleep(0.1)
        s.recv(4096)
        
        s.send(admin_key.encode() + b'\n')
        time.sleep(0.1)
        response = s.recv(4096).decode()
        
        if "Logged in as" in response:
            # Get message
            s.send(b"1\n")
            time.sleep(0.1)
            msg_response = s.recv(4096).decode()
            
            message = None
            if "Your message:" in msg_response:
                message = msg_response.split("Your message: ")[1].strip().split('\n')[0]
            
            s.close()
            return True, message
        else:
            s.close()
            return False, None
            
    except Exception as e:
        return False, None

def main():
    print("Fixed seed attack - trying systematic approach")
    
    admin_usernames = ["Admin001", "Admin002", "Admin003"]
    
    # Maybe the server uses a very simple seed or no seed at all
    # Let's try a wider range of simple seeds
    
    seeds_to_try = []
    
    # Add simple numeric seeds
    seeds_to_try.extend(range(0, 1000))
    
    # Add some common CTF seeds
    seeds_to_try.extend([1337, 31337, 12345, 54321, 9999, 8888, 7777])
    
    # Add time-based seeds (maybe server started at a round time)
    current_time = int(time.time())
    
    # Try times rounded to hours, days, etc.
    for hours_back in range(0, 48):  # Last 48 hours
        rounded_time = current_time - (current_time % 3600) - (hours_back * 3600)
        seeds_to_try.append(rounded_time)
    
    # Try times rounded to days
    for days_back in range(0, 7):  # Last week
        rounded_time = current_time - (current_time % 86400) - (days_back * 86400)
        seeds_to_try.append(rounded_time)
    
    print(f"Testing {len(seeds_to_try)} different seeds...")
    
    for i, seed in enumerate(seeds_to_try):
        if i % 100 == 0:
            print(f"Progress: {i}/{len(seeds_to_try)}")
        
        random.seed(seed)
        
        # Generate admin keys
        admin_keys = []
        for admin_user in admin_usernames:
            key = get_key(admin_user)
            admin_keys.append(key)
        
        # Test first admin account
        success, message = test_login(admin_usernames[0], admin_keys[0])
        
        if success:
            print(f"\n🎉 FOUND WORKING SEED: {seed}")
            print(f"Admin001 key: {admin_keys[0]}")
            print(f"Flag part 1: {message}")
            
            flag_parts = [message]
            
            # Test other admin accounts
            for i in range(1, 3):
                success, message = test_login(admin_usernames[i], admin_keys[i])
                if success:
                    print(f"Admin00{i+1} key: {admin_keys[i]}")
                    print(f"Flag part {i+1}: {message}")
                    flag_parts.append(message)
                else:
                    print(f"Failed to login as {admin_usernames[i]}")
            
            if len(flag_parts) == 3:
                print(f"\n🎉 FULL FLAG: {''.join(flag_parts)}")
            else:
                print(f"Only got {len(flag_parts)} flag parts")
            
            return
        
        # Small delay to avoid overwhelming the server
        if i % 10 == 0:
            time.sleep(0.1)
    
    print("No working seed found in the tested range")

if __name__ == "__main__":
    main()
