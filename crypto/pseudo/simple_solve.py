#!/usr/bin/env python3
import random
import base64
import socket
import time

def get_key(username):
    """Replicate the key generation from server.py"""
    num_bits = 8 * len(username)
    rand = random.getrandbits(num_bits)
    rand_bits = bin(rand)[2:].zfill(num_bits)
    username_bits = ''.join([bin(ord(char))[2:].zfill(8) for char in username])
    xor_bits = ''.join([str(int(rand_bits[i]) ^ int(username_bits[i])) for i in range(num_bits)])
    xor_result = int(xor_bits, 2)
    shifted = ((xor_result << 3) & (1 << (num_bits + 3)) - 1) ^ 0x5A
    byte_data = shifted.to_bytes((shifted.bit_length() + 7) // 8, 'big')
    key = base64.b64encode(byte_data).decode('utf-8')
    return key

def main():
    print("Simple approach: trying common seeds...")
    
    # The server likely uses a simple or default seed
    # Let's try the most common ones
    
    admin_usernames = ["Admin001", "Admin002", "Admin003"]
    
    # Try seed = 1 (very common in CTFs)
    print("Trying seed = 1...")
    random.seed(1)
    
    admin_keys = []
    for admin_user in admin_usernames:
        key = get_key(admin_user)
        admin_keys.append(key)
        print(f"Predicted key for {admin_user}: {key}")
    
    # Connect and try to login
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        s.connect(("tjc.tf", 31400))
        s.recv(4096)  # Welcome message
        
        flag_parts = []
        
        for i, admin_user in enumerate(admin_usernames):
            print(f"\nTrying to login as {admin_user}...")
            
            # Send option 1 (Sign-In)
            s.send(b"1\n")
            time.sleep(0.1)
            s.recv(4096)
            
            # Send username
            s.send(admin_user.encode() + b'\n')
            time.sleep(0.1)
            s.recv(4096)
            
            # Send key
            s.send(admin_keys[i].encode() + b'\n')
            time.sleep(0.1)
            response = s.recv(4096).decode()
            
            if "Logged in as" in response:
                print(f"SUCCESS! Logged in as {admin_user}")
                
                # View message
                s.send(b"1\n")
                time.sleep(0.1)
                msg_response = s.recv(4096).decode()
                
                if "Your message:" in msg_response:
                    message = msg_response.split("Your message: ")[1].strip().split('\n')[0]
                    print(f"Flag part {i+1}: {message}")
                    flag_parts.append(message)
                
                # Logout
                s.send(b"l\n")
                time.sleep(0.1)
                s.recv(4096)
            else:
                print(f"Failed to login as {admin_user}")
                print(f"Response: {response}")
        
        if len(flag_parts) == 3:
            print(f"\nFull flag: {''.join(flag_parts)}")
        
        s.close()
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
