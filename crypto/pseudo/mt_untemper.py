#!/usr/bin/env python3
import random
import base64
import socket
import time

# MT19937 constants
w, n, m, r = 32, 624, 397, 31
a = 0x9908B0DF
u, d = 11, 0xFFFFFFFF
s, b = 7, 0x9D2C5680
t, c = 15, 0xEFC60000
l = 18
f = 1812433253

def get_key(username):
    """Replicate the key generation from server.py"""
    num_bits = 8 * len(username)
    rand = random.getrandbits(num_bits)
    rand_bits = bin(rand)[2:].zfill(num_bits)
    username_bits = ''.join([bin(ord(char))[2:].zfill(8) for char in username])
    xor_bits = ''.join([str(int(rand_bits[i]) ^ int(username_bits[i])) for i in range(num_bits)])
    xor_result = int(xor_bits, 2)
    shifted = ((xor_result << 3) & (1 << (num_bits + 3)) - 1) ^ 0x5A
    byte_data = shifted.to_bytes((shifted.bit_length() + 7) // 8, 'big')
    key = base64.b64encode(byte_data).decode('utf-8')
    return key

def reverse_key_to_rand(username, key):
    """Reverse the key generation to get the original random number"""
    try:
        byte_data = base64.b64decode(key)
        shifted = int.from_bytes(byte_data, 'big')
        num_bits = 8 * len(username)
        xor_result = (shifted ^ 0x5A) >> 3
        xor_bits = bin(xor_result)[2:].zfill(num_bits)
        username_bits = ''.join([bin(ord(char))[2:].zfill(8) for char in username])
        rand_bits = ''.join([str(int(xor_bits[i]) ^ int(username_bits[i])) for i in range(num_bits)])
        rand = int(rand_bits, 2)
        return rand
    except:
        return None

def untemper(y):
    """Reverse the MT19937 tempering function"""
    # Reverse the tempering steps
    y = y ^ (y >> l)
    y = y ^ ((y << t) & c)
    y = y ^ ((y << s) & b)
    y = y ^ ((y >> u) & d)
    return y & 0xFFFFFFFF

def temper(y):
    """Apply MT19937 tempering function"""
    y = y ^ ((y >> u) & d)
    y = y ^ ((y << s) & b)
    y = y ^ ((y << t) & c)
    y = y ^ (y >> l)
    return y & 0xFFFFFFFF

def create_account_and_get_key(s, username):
    """Create an account and extract the key"""
    try:
        s.send(b"2\n")
        time.sleep(0.1)
        s.recv(4096)
        
        s.send(username.encode() + b'\n')
        time.sleep(0.1)
        response = s.recv(4096).decode()
        
        if "Your sign-in key is:" in response:
            for line in response.split('\n'):
                if "Your sign-in key is:" in line:
                    key = line.split("Your sign-in key is: ")[1].strip()
                    return key
        return None
    except:
        return None

def extract_32bit_from_partial(partial_rand, bits_used):
    """
    Try to extract possible 32-bit values that could have produced the partial random
    This is a simplified approach - in practice, we'd need more sophisticated techniques
    """
    if bits_used == 32:
        return [partial_rand]
    
    # For partial bits, we need to try all possible values for the missing bits
    missing_bits = 32 - bits_used
    possible_values = []
    
    for i in range(min(1000, 2**missing_bits)):  # Limit to avoid memory issues
        full_value = (partial_rand << missing_bits) | i
        if full_value < 2**32:
            possible_values.append(full_value)
    
    return possible_values

def main():
    print("MT19937 Untemper Attack")
    
    try:
        # Connect and gather observations
        s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        s.settimeout(30)
        s.connect(("tjc.tf", 31400))
        s.recv(4096)  # Welcome message
        
        print("Creating accounts to gather MT outputs...")
        
        # Create accounts to get 32-bit outputs (8 characters = 64 bits, but we'll work with what we can get)
        test_accounts = [
            ("abcd", 32),      # 4 chars = 32 bits
            ("efgh", 32),      # 4 chars = 32 bits  
            ("ijkl", 32),      # 4 chars = 32 bits
            ("mnop", 32),      # 4 chars = 32 bits
        ]
        
        mt_outputs = []
        
        for username, expected_bits in test_accounts:
            key = create_account_and_get_key(s, username)
            if key:
                print(f"Created {username} with key: {key}")
                rand_val = reverse_key_to_rand(username, key)
                if rand_val is not None:
                    print(f"  Extracted {expected_bits}-bit random: {rand_val}")
                    
                    # Try to get possible 32-bit MT outputs
                    possible_32bit = extract_32bit_from_partial(rand_val, expected_bits)
                    print(f"  Possible 32-bit values: {len(possible_32bit)}")
                    
                    if len(possible_32bit) <= 10:  # Only if manageable number
                        for val in possible_32bit:
                            # Untemper to get the internal state
                            internal_state = untemper(val)
                            mt_outputs.append(internal_state)
                            print(f"    Internal state candidate: {internal_state}")
            else:
                print(f"Failed to create account for {username}")
        
        s.close()
        
        if len(mt_outputs) < 3:
            print("Not enough MT outputs for state recovery")
            return
        
        print(f"\nGathered {len(mt_outputs)} potential MT internal states")
        
        # Now try to use these to predict the admin keys
        # This is a simplified approach - we'd need the full 624 states for complete recovery
        
        # Try to find a pattern or use brute force with the information we have
        print("Attempting to predict admin keys...")
        
        # Since we don't have enough for full state recovery, let's try a different approach
        # Maybe we can use the fact that the admin accounts were created first
        
        # Try some common seeds that might produce the observed outputs
        for seed in range(100000):
            if seed % 10000 == 0:
                print(f"Trying seed {seed}...")
            
            random.seed(seed)
            
            # Generate what would be the admin keys
            admin_keys = []
            for admin_user in ["Admin001", "Admin002", "Admin003"]:
                key = get_key(admin_user)
                admin_keys.append(key)
            
            # Check if the subsequent outputs match what we observed
            matches = 0
            for username, _ in test_accounts:
                predicted_key = get_key(username)
                # We'd need to compare this with what we actually observed
                # This is a simplified check
                matches += 1  # Placeholder
            
            # If we have a good match, try the admin keys
            if matches >= len(test_accounts):  # Simplified condition
                print(f"Potential seed found: {seed}")
                
                # Try to login with the first admin key
                try:
                    s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    s.settimeout(5)
                    s.connect(("tjc.tf", 31400))
                    s.recv(4096)
                    
                    # Test Admin001
                    s.send(b"1\n")
                    time.sleep(0.1)
                    s.recv(4096)
                    
                    s.send(b"Admin001\n")
                    time.sleep(0.1)
                    s.recv(4096)
                    
                    s.send(admin_keys[0].encode() + b'\n')
                    time.sleep(0.1)
                    response = s.recv(4096).decode()
                    
                    if "Logged in as" in response:
                        print(f"SUCCESS! Found working seed: {seed}")
                        print(f"Admin001 key: {admin_keys[0]}")
                        
                        # Get flag part
                        s.send(b"1\n")
                        time.sleep(0.1)
                        msg_response = s.recv(4096).decode()
                        
                        if "Your message:" in msg_response:
                            message = msg_response.split("Your message: ")[1].strip().split('\n')[0]
                            print(f"Flag part 1: {message}")
                        
                        s.close()
                        
                        # Try other admin accounts
                        flag_parts = [message] if 'message' in locals() else []
                        
                        for i in range(1, 3):
                            try:
                                s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                                s.connect(("tjc.tf", 31400))
                                s.recv(4096)
                                
                                s.send(b"1\n")
                                time.sleep(0.1)
                                s.recv(4096)
                                
                                s.send(f"Admin00{i+1}\n".encode())
                                time.sleep(0.1)
                                s.recv(4096)
                                
                                s.send(admin_keys[i].encode() + b'\n')
                                time.sleep(0.1)
                                response = s.recv(4096).decode()
                                
                                if "Logged in as" in response:
                                    s.send(b"1\n")
                                    time.sleep(0.1)
                                    msg_response = s.recv(4096).decode()
                                    
                                    if "Your message:" in msg_response:
                                        message = msg_response.split("Your message: ")[1].strip().split('\n')[0]
                                        print(f"Flag part {i+1}: {message}")
                                        flag_parts.append(message)
                                
                                s.close()
                                
                            except Exception as e:
                                print(f"Error with Admin00{i+1}: {e}")
                        
                        if len(flag_parts) == 3:
                            print(f"\n🎉 FULL FLAG: {''.join(flag_parts)}")
                            return
                        
                        return
                    
                    s.close()
                    
                except Exception as e:
                    continue
        
        print("Could not find working seed with this approach")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
