#!/usr/bin/env python3
import random
import base64
import socket
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed

def get_key(username):
    """Replicate the key generation from server.py"""
    num_bits = 8 * len(username)
    rand = random.getrandbits(num_bits)
    rand_bits = bin(rand)[2:].zfill(num_bits)
    username_bits = ''.join([bin(ord(char))[2:].zfill(8) for char in username])
    xor_bits = ''.join([str(int(rand_bits[i]) ^ int(username_bits[i])) for i in range(num_bits)])
    xor_result = int(xor_bits, 2)
    shifted = ((xor_result << 3) & (1 << (num_bits + 3)) - 1) ^ 0x5A
    byte_data = shifted.to_bytes((shifted.bit_length() + 7) // 8, 'big')
    key = base64.b64encode(byte_data).decode('utf-8')
    return key

def test_admin_login(admin_user, admin_key):
    """Test login with a specific admin user and key"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        s.settimeout(5)
        s.connect(("tjc.tf", 31400))
        s.recv(4096)  # Welcome message
        
        # Login
        s.send(b"1\n")
        time.sleep(0.05)
        s.recv(4096)
        
        s.send(admin_user.encode() + b'\n')
        time.sleep(0.05)
        s.recv(4096)
        
        s.send(admin_key.encode() + b'\n')
        time.sleep(0.05)
        response = s.recv(4096).decode()
        
        if "Logged in as" in response:
            # Get message
            s.send(b"1\n")
            time.sleep(0.05)
            msg_response = s.recv(4096).decode()
            
            message = None
            if "Your message:" in msg_response:
                message = msg_response.split("Your message: ")[1].strip().split('\n')[0]
            
            s.close()
            return True, message
        else:
            s.close()
            return False, None
            
    except Exception as e:
        return False, None

def test_seed_range(start_seed, end_seed):
    """Test a range of seeds"""
    for seed in range(start_seed, end_seed):
        random.seed(seed)
        
        # Generate admin keys
        admin_keys = []
        for admin_user in ["Admin001", "Admin002", "Admin003"]:
            key = get_key(admin_user)
            admin_keys.append(key)
        
        # Test first admin account quickly
        success, message = test_admin_login("Admin001", admin_keys[0])
        
        if success:
            print(f"\n🎉 FOUND WORKING SEED: {seed}")
            print(f"Admin001 key: {admin_keys[0]}")
            print(f"Flag part 1: {message}")
            
            flag_parts = [message]
            
            # Test other admin accounts
            for i in range(1, 3):
                success, message = test_admin_login(f"Admin00{i+1}", admin_keys[i])
                if success:
                    print(f"Admin00{i+1} key: {admin_keys[i]}")
                    print(f"Flag part {i+1}: {message}")
                    flag_parts.append(message)
                else:
                    print(f"Failed to login as Admin00{i+1}")
            
            if len(flag_parts) == 3:
                print(f"\n🎉 FULL FLAG: {''.join(flag_parts)}")
            else:
                print(f"Only got {len(flag_parts)} flag parts")
            
            return seed
    
    return None

def main():
    print("Final systematic brute force attempt")
    print("This will try many seeds systematically...")
    
    # Try different ranges of seeds
    ranges_to_try = [
        (0, 10000),           # Simple numeric seeds
        (1000000, 1010000),   # Larger numeric seeds
        (31337, 31400),       # CTF-style seeds around port number
        (12345, 12400),       # Common seeds
        (1337, 1400),         # Leet speak
    ]
    
    # Also try time-based seeds
    current_time = int(time.time())
    
    # Try times rounded to different intervals
    for hours_back in range(0, 48):  # Last 48 hours
        rounded_time = current_time - (current_time % 3600) - (hours_back * 3600)
        ranges_to_try.append((rounded_time, rounded_time + 1))
    
    for minutes_back in range(0, 120, 5):  # Last 2 hours, every 5 minutes
        rounded_time = current_time - (current_time % 300) - (minutes_back * 300)
        ranges_to_try.append((rounded_time, rounded_time + 1))
    
    print(f"Will test {len(ranges_to_try)} different ranges")
    
    for i, (start, end) in enumerate(ranges_to_try):
        print(f"\nTesting range {i+1}/{len(ranges_to_try)}: {start} to {end}")
        
        result = test_seed_range(start, end)
        if result is not None:
            print(f"Success with seed {result}!")
            return
        
        # Small delay to avoid overwhelming the server
        time.sleep(0.1)
    
    print("No working seed found in any tested range")
    
    # Last resort: try some very specific seeds that might be used in CTFs
    print("\nTrying some very specific CTF-style seeds...")
    specific_seeds = [
        0x1337, 0x31337, 0xdeadbeef, 0xcafebabe, 0xfeedface,
        0x13371337, 0x12345678, 0x87654321, 0xabcdef01,
        2023, 2024, 2025,  # Years
        31400,  # Port number
        ord('t') + ord('j') + ord('c') + ord('t') + ord('f'),  # tjctf
    ]
    
    for seed in specific_seeds:
        print(f"Trying specific seed: {seed} (0x{seed:x})")
        
        random.seed(seed)
        admin_keys = []
        for admin_user in ["Admin001", "Admin002", "Admin003"]:
            key = get_key(admin_user)
            admin_keys.append(key)
        
        success, message = test_admin_login("Admin001", admin_keys[0])
        if success:
            print(f"SUCCESS with seed {seed}!")
            
            flag_parts = [message]
            for i in range(1, 3):
                success, message = test_admin_login(f"Admin00{i+1}", admin_keys[i])
                if success:
                    flag_parts.append(message)
            
            if len(flag_parts) == 3:
                print(f"\n🎉 FULL FLAG: {''.join(flag_parts)}")
                return
        
        time.sleep(0.1)
    
    print("All attempts failed. The vulnerability might be different than expected.")

if __name__ == "__main__":
    main()
