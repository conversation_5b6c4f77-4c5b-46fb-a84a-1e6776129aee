#!/usr/bin/env python3
import random
import base64
import socket
import time

def get_key(username):
    """Replicate the key generation from server.py"""
    num_bits = 8 * len(username)
    rand = random.getrandbits(num_bits)
    rand_bits = bin(rand)[2:].zfill(num_bits)
    username_bits = ''.join([bin(ord(char))[2:].zfill(8) for char in username])
    xor_bits = ''.join([str(int(rand_bits[i]) ^ int(username_bits[i])) for i in range(num_bits)])
    xor_result = int(xor_bits, 2)
    shifted = ((xor_result << 3) & (1 << (num_bits + 3)) - 1) ^ 0x5A
    byte_data = shifted.to_bytes((shifted.bit_length() + 7) // 8, 'big')
    key = base64.b64encode(byte_data).decode('utf-8')
    return key

def try_login_with_seed(seed):
    """Try to login with admin accounts using a specific seed"""
    print(f"\nTrying seed: {seed}")
    
    if seed is not None:
        random.seed(seed)
    else:
        random.seed()
    
    admin_usernames = ["Admin001", "Admin002", "Admin003"]
    admin_keys = []
    
    for admin_user in admin_usernames:
        key = get_key(admin_user)
        admin_keys.append(key)
        print(f"  Predicted key for {admin_user}: {key}")
    
    try:
        # Connect to server
        s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        s.settimeout(10)
        s.connect(("tjc.tf", 31400))
        
        # Receive welcome message
        welcome = s.recv(4096).decode()
        print("Connected to server")
        
        flag_parts = []
        
        for i, admin_user in enumerate(admin_usernames):
            print(f"Trying to login as {admin_user}...")
            
            try:
                # Send option 1 (Sign-In)
                s.send(b"1\n")
                time.sleep(0.2)
                response = s.recv(4096).decode()
                
                # Send username
                s.send(admin_user.encode() + b'\n')
                time.sleep(0.2)
                response = s.recv(4096).decode()
                
                # Send key
                s.send(admin_keys[i].encode() + b'\n')
                time.sleep(0.2)
                response = s.recv(4096).decode()
                
                if "Logged in as" in response:
                    print(f"SUCCESS! Logged in as {admin_user}")
                    
                    # Send option 1 (View Message)
                    s.send(b"1\n")
                    time.sleep(0.2)
                    msg_response = s.recv(4096).decode()
                    
                    if "Your message:" in msg_response:
                        # Extract message
                        lines = msg_response.split('\n')
                        for line in lines:
                            if "Your message:" in line:
                                message = line.split("Your message: ")[1].strip()
                                print(f"Flag part {i+1}: {message}")
                                flag_parts.append(message)
                                break
                    
                    # Send option l (Logout)
                    s.send(b"l\n")
                    time.sleep(0.2)
                    s.recv(4096)
                    
                else:
                    print(f"Failed to login as {admin_user}")
                    print(f"Response: {response[:200]}...")
                    
            except Exception as e:
                print(f"Error during login attempt for {admin_user}: {e}")
        
        s.close()
        
        if len(flag_parts) == 3:
            full_flag = ''.join(flag_parts)
            print(f"\n🎉 FULL FLAG: {full_flag}")
            return True
        else:
            print(f"Only got {len(flag_parts)} flag parts")
            return False
            
    except Exception as e:
        print(f"Connection error: {e}")
        return False

def main():
    print("CTF Solver for pseudo-secure challenge")
    print("Attempting to predict admin keys using different seeds...")
    
    # Try different seeds that are commonly used
    seeds_to_try = [1, 0, 42, 1337, 12345, None]
    
    for seed in seeds_to_try:
        if try_login_with_seed(seed):
            print("Challenge solved!")
            return
        
        time.sleep(1)  # Brief pause between attempts
    
    print("Failed to solve with common seeds. The server might be using a different approach.")

if __name__ == "__main__":
    main()
