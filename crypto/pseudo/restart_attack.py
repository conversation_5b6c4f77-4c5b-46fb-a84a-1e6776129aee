#!/usr/bin/env python3
import random
import base64
import socket
import time
import threading

def get_key(username):
    """Replicate the key generation from server.py"""
    num_bits = 8 * len(username)
    rand = random.getrandbits(num_bits)
    rand_bits = bin(rand)[2:].zfill(num_bits)
    username_bits = ''.join([bin(ord(char))[2:].zfill(8) for char in username])
    xor_bits = ''.join([str(int(rand_bits[i]) ^ int(username_bits[i])) for i in range(num_bits)])
    xor_result = int(xor_bits, 2)
    shifted = ((xor_result << 3) & (1 << (num_bits + 3)) - 1) ^ 0x5A
    byte_data = shifted.to_bytes((shifted.bit_length() + 7) // 8, 'big')
    key = base64.b64encode(byte_data).decode('utf-8')
    return key

def test_seed_quickly(seed):
    """Test a seed quickly by trying to login as Admin001"""
    try:
        random.seed(seed)
        admin_key = get_key("Admin001")
        
        s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        s.settimeout(3)
        s.connect(("tjc.tf", 31400))
        s.recv(4096)
        
        # Try to login as Admin001
        s.send(b"1\n")
        time.sleep(0.05)
        s.recv(4096)
        
        s.send(b"Admin001\n")
        time.sleep(0.05)
        s.recv(4096)
        
        s.send(admin_key.encode() + b'\n')
        time.sleep(0.05)
        response = s.recv(4096).decode()
        
        s.close()
        
        if "Logged in as" in response:
            return True, admin_key
        else:
            return False, None
            
    except:
        return False, None

def main():
    print("Restart-based attack - trying to catch server after restart")
    
    # Maybe the server restarts every few minutes or at specific times
    # Let's try seeds based on recent times
    
    current_time = int(time.time())
    print(f"Current time: {current_time}")
    
    # Try times rounded to different intervals
    test_times = []
    
    # Every 5 minutes for the last hour
    for minutes_back in range(0, 60, 5):
        rounded_time = current_time - (current_time % 300) - (minutes_back * 300)
        test_times.append(rounded_time)
    
    # Every 10 minutes for the last 2 hours  
    for minutes_back in range(0, 120, 10):
        rounded_time = current_time - (current_time % 600) - (minutes_back * 600)
        test_times.append(rounded_time)
    
    # Every hour for the last day
    for hours_back in range(0, 24):
        rounded_time = current_time - (current_time % 3600) - (hours_back * 3600)
        test_times.append(rounded_time)
    
    # Remove duplicates and sort
    test_times = sorted(list(set(test_times)), reverse=True)
    
    print(f"Testing {len(test_times)} potential restart times...")
    
    for i, test_time in enumerate(test_times):
        if i % 10 == 0:
            print(f"Progress: {i}/{len(test_times)}")
        
        success, admin_key = test_seed_quickly(test_time)
        
        if success:
            print(f"\n🎉 FOUND WORKING SEED: {test_time}")
            print(f"Admin001 key: {admin_key}")
            
            # Generate all admin keys
            random.seed(test_time)
            admin_usernames = ["Admin001", "Admin002", "Admin003"]
            admin_keys = []
            
            for admin_user in admin_usernames:
                key = get_key(admin_user)
                admin_keys.append(key)
                print(f"Key for {admin_user}: {key}")
            
            # Get all flag parts
            try:
                s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                s.connect(("tjc.tf", 31400))
                s.recv(4096)
                
                flag_parts = []
                
                for i, admin_user in enumerate(admin_usernames):
                    try:
                        s.send(b"1\n")
                        time.sleep(0.1)
                        s.recv(4096)
                        
                        s.send(admin_user.encode() + b'\n')
                        time.sleep(0.1)
                        s.recv(4096)
                        
                        s.send(admin_keys[i].encode() + b'\n')
                        time.sleep(0.1)
                        response = s.recv(4096).decode()
                        
                        if "Logged in as" in response:
                            s.send(b"1\n")
                            time.sleep(0.1)
                            msg_response = s.recv(4096).decode()
                            
                            if "Your message:" in msg_response:
                                message = msg_response.split("Your message: ")[1].strip().split('\n')[0]
                                print(f"Flag part {i+1}: {message}")
                                flag_parts.append(message)
                            
                            s.send(b"l\n")
                            time.sleep(0.1)
                            s.recv(4096)
                        
                    except Exception as e:
                        print(f"Error with {admin_user}: {e}")
                
                s.close()
                
                if len(flag_parts) == 3:
                    print(f"\n🎉 FULL FLAG: {''.join(flag_parts)}")
                else:
                    print(f"Only got {len(flag_parts)} flag parts")
                    
            except Exception as e:
                print(f"Error getting flag parts: {e}")
            
            return
        
        # Small delay to avoid overwhelming the server
        time.sleep(0.1)
    
    print("No working seed found")

if __name__ == "__main__":
    main()
