#!/usr/bin/env python3
import random
import base64

def get_key(username):
    """Replicate the key generation from server.py"""
    num_bits = 8 * len(username)
    rand = random.getrandbits(num_bits)
    rand_bits = bin(rand)[2:].zfill(num_bits)
    username_bits = ''.join([bin(ord(char))[2:].zfill(8) for char in username])
    xor_bits = ''.join([str(int(rand_bits[i]) ^ int(username_bits[i])) for i in range(num_bits)])
    xor_result = int(xor_bits, 2)
    shifted = ((xor_result << 3) & (1 << (num_bits + 3)) - 1) ^ 0x5A
    byte_data = shifted.to_bytes((shifted.bit_length() + 7) // 8, 'big')
    key = base64.b64encode(byte_data).decode('utf-8')
    return key

# Test different seeds
admin_usernames = ["Admin001", "Admin002", "Admin003"]

print("Testing different seeds:")
for seed in [1, 0, 42, 1337, None]:
    print(f"\nSeed: {seed}")
    if seed is not None:
        random.seed(seed)
    else:
        random.seed()
    
    for admin_user in admin_usernames:
        key = get_key(admin_user)
        print(f"  {admin_user}: {key}")
