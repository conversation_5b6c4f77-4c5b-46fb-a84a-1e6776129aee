from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad
import random
import itertools

def dec(ct, k1, k2, k3, k4):
    key2 = k4+k3
    cipher = AES.new(key2, mode=AES.MODE_ECB)
    ct1 = cipher.decrypt(ct)
    key1 = k1+k2
    cipher = AES.new(key1, mode=AES.MODE_ECB)
    pt = cipher.decrypt(ct1)
    return unpad(pt, 16)

# Read the encrypted data
with open('out.txt', 'r') as f:
    lines = f.read().strip().split('\n')
    enc_example = bytes.fromhex(lines[0])
    enc_flag = bytes.fromhex(lines[1])

known_pt = b"example"

# Get the deterministic parts
myrandom = random.Random(42)
k1 = myrandom.randbytes(8)
choices = list(myrandom.randbytes(6))

print(f"k1 = k3: {k1.hex()}")
print(f"choices: {choices}")

# Since k1 = k3 and choices are the same for both gen() calls,
# we need to find k2 and k4 where each is 8 bytes chosen from 4 possible values
# That's 4^8 = 65536 possibilities for each, total 4^16 = ~4 billion

print("Starting systematic brute force...")
print("This will try all possible combinations of k2 and k4")

count = 0
found = False

# Use itertools.product to generate all combinations
for k2_indices in itertools.product(range(4), repeat=8):
    if count % 10000 == 0:
        print(f"Tried {count} combinations...")
    
    k2 = b''.join(bytes([choices[i]]) for i in k2_indices)
    
    for k4_indices in itertools.product(range(4), repeat=8):
        count += 1
        
        k4 = b''.join(bytes([choices[i]]) for i in k4_indices)
        
        try:
            decrypted = dec(enc_example, k1, k2, k1, k4)  # k3 = k1
            if decrypted == known_pt:
                print(f"\nSUCCESS after {count} attempts!")
                print(f"k1: {k1.hex()}")
                print(f"k2: {k2.hex()}")
                print(f"k3: {k1.hex()}")
                print(f"k4: {k4.hex()}")
                print(f"k2_indices: {k2_indices}")
                print(f"k4_indices: {k4_indices}")
                
                # Decrypt the flag
                flag = dec(enc_flag, k1, k2, k1, k4)
                print(f"Flag: {flag.decode()}")
                found = True
                break
        except:
            continue
    
    if found:
        break

if not found:
    print(f"Exhausted all {4**16} combinations without success")
    print("There might be an error in the approach")
