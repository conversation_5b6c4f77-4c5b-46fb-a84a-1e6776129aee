import random

# Let's understand the exact behavior of the random module
print("Testing random behavior...")

# Test 1: What happens with no seed?
print("\n=== Test 1: No explicit seed ===")
for i in range(10):
    print(f"random.randint(0, 3): {random.randint(0, 3)}")

# Test 2: What happens when we create a new Random instance?
print("\n=== Test 2: Random(42) instance ===")
myrandom = random.Random(42)
for i in range(10):
    print(f"myrandom.randint(0, 3): {myrandom.randint(0, 3)}")

# Test 3: Mix of both
print("\n=== Test 3: Mix of Random(42) and global random ===")
myrandom = random.Random(42)
k1 = myrandom.randbytes(8)
choices = list(myrandom.randbytes(6))
print(f"k1: {k1.hex()}")
print(f"choices: {[hex(x) for x in choices]}")

print("Now using global random with choices:")
for i in range(8):
    idx = random.randint(0, 3)
    print(f"random.randint(0, 3) = {idx}, choices[{idx}] = {hex(choices[idx])}")

# Test 4: Let's see if the global random state affects subsequent Random(42) instances
print("\n=== Test 4: Does global random affect Random(42)? ===")
random.seed(123)  # Set a specific seed for global random
print("Set global random seed to 123")

myrandom1 = random.Random(42)
print(f"First Random(42) randbytes: {myrandom1.randbytes(4).hex()}")

# Use global random
print(f"Global random.randint(0, 3): {random.randint(0, 3)}")

myrandom2 = random.Random(42)
print(f"Second Random(42) randbytes: {myrandom2.randbytes(4).hex()}")

print("Are they the same?", myrandom1.randbytes(4).hex() == myrandom2.randbytes(4).hex())
