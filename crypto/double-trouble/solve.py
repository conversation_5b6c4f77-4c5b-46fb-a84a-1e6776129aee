from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad
import random

def gen():
    myrandom = random.Random(42)
    k1 = myrandom.randbytes(8)
    choices = list(myrandom.randbytes(6))
    k2 = b''
    for _ in range(8):
        k2 += bytes([choices[random.randint(0, 3)]])
    return k1, k2

def dec(ct, k1, k2, k3, k4):
    key2 = k4+k3
    cipher = AES.new(key2, mode=AES.MODE_ECB)
    ct1 = cipher.decrypt(ct)
    key1 = k1+k2
    cipher = AES.new(key1, mode=AES.MODE_ECB)
    pt = cipher.decrypt(ct1)
    return unpad(pt, 16)

# Read the encrypted data
with open('out.txt', 'r') as f:
    lines = f.read().strip().split('\n')
    enc_example = bytes.fromhex(lines[0])
    enc_flag = bytes.fromhex(lines[1])

known_pt = b"example"

print("Trying to reproduce the exact key generation...")

# The key insight: Python's random module uses a default seed based on system time
# But in a CTF, they probably used a predictable state
# Let's try common scenarios:

scenarios = [
    ("No seed (default)", None),
    ("Seed 0", 0),
    ("Seed 1", 1),
    ("Seed 42", 42),
    ("Seed 1337", 1337),
]

for desc, seed in scenarios:
    print(f"Trying {desc}...")

    if seed is not None:
        random.seed(seed)

    # Generate keys exactly like the original script
    k1, k2 = gen()
    k3, k4 = gen()

    try:
        decrypted = dec(enc_example, k1, k2, k3, k4)
        if decrypted == known_pt:
            print(f"SUCCESS with {desc}!")
            print(f"k1: {k1.hex()}")
            print(f"k2: {k2.hex()}")
            print(f"k3: {k3.hex()}")
            print(f"k4: {k4.hex()}")

            # Decrypt the flag
            flag = dec(enc_flag, k1, k2, k3, k4)
            print(f"Flag: {flag.decode()}")
            exit()
    except Exception as e:
        print(f"Failed with {desc}: {e}")
        continue

print("None of the common scenarios worked. Let's try a broader range...")

# Try a range of seeds
for seed in range(1000):
    if seed % 100 == 0:
        print(f"Trying seed {seed}...")

    random.seed(seed)

    # Generate keys exactly like the original script
    k1, k2 = gen()
    k3, k4 = gen()

    try:
        decrypted = dec(enc_example, k1, k2, k3, k4)
        if decrypted == known_pt:
            print(f"SUCCESS with seed {seed}!")
            print(f"k1: {k1.hex()}")
            print(f"k2: {k2.hex()}")
            print(f"k3: {k3.hex()}")
            print(f"k4: {k4.hex()}")

            # Decrypt the flag
            flag = dec(enc_flag, k1, k2, k3, k4)
            print(f"Flag: {flag.decode()}")
            exit()
    except Exception as e:
        continue

print("Could not find the correct seed in range 0-999")
