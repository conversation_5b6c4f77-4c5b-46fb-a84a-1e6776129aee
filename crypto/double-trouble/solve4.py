from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad
import random

def gen():
    myrandom = random.Random(42)
    k1 = myrandom.randbytes(8)
    choices = list(myrandom.randbytes(6))
    k2 = b''
    for _ in range(8):
        k2 += bytes([choices[random.randint(0, 3)]])
    return k1, k2

def dec(ct, k1, k2, k3, k4):
    key2 = k4+k3
    cipher = AES.new(key2, mode=AES.MODE_ECB)
    ct1 = cipher.decrypt(ct)
    key1 = k1+k2
    cipher = AES.new(key1, mode=AES.MODE_ECB)
    pt = cipher.decrypt(ct1)
    return unpad(pt, 16)

# Read the encrypted data
with open('out.txt', 'r') as f:
    lines = f.read().strip().split('\n')
    enc_example = bytes.fromhex(lines[0])
    enc_flag = bytes.fromhex(lines[1])

known_pt = b"example"

print("Simulating the exact execution flow...")

# Let's try many different initial seeds for the global random state
for seed in range(10000):
    if seed % 1000 == 0:
        print(f"Trying seed {seed}...")
    
    # Reset global random state
    random.seed(seed)
    
    # Execute exactly like the original script
    k1, k2 = gen()  # First call modifies global random state
    k3, k4 = gen()  # Second call uses the modified state
    
    try:
        decrypted = dec(enc_example, k1, k2, k3, k4)
        if decrypted == known_pt:
            print(f"SUCCESS with seed {seed}!")
            print(f"k1: {k1.hex()}")
            print(f"k2: {k2.hex()}")
            print(f"k3: {k3.hex()}")
            print(f"k4: {k4.hex()}")
            
            # Decrypt the flag
            flag = dec(enc_flag, k1, k2, k3, k4)
            print(f"Flag: {flag.decode()}")
            break
    except Exception as e:
        continue
else:
    print("Could not find the correct seed in range 0-9999")
    
    # Let's also try with no seed (default behavior)
    print("Trying with default random state...")
    
    # Don't call random.seed() - use whatever the default state is
    k1, k2 = gen()
    k3, k4 = gen()
    
    try:
        decrypted = dec(enc_example, k1, k2, k3, k4)
        if decrypted == known_pt:
            print("SUCCESS with default state!")
            print(f"k1: {k1.hex()}")
            print(f"k2: {k2.hex()}")
            print(f"k3: {k3.hex()}")
            print(f"k4: {k4.hex()}")
            
            # Decrypt the flag
            flag = dec(enc_flag, k1, k2, k3, k4)
            print(f"Flag: {flag.decode()}")
        else:
            print(f"Default state decrypted to: {decrypted}")
    except Exception as e:
        print(f"Default state failed: {e}")
