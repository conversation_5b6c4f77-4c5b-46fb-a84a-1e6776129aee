from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad
import random

def dec(ct, k1, k2, k3, k4):
    key2 = k4+k3
    cipher = AES.new(key2, mode=AES.MODE_ECB)
    ct1 = cipher.decrypt(ct)
    key1 = k1+k2
    cipher = AES.new(key1, mode=AES.MODE_ECB)
    pt = cipher.decrypt(ct1)
    return unpad(pt, 16)

# Read the encrypted data
with open('out.txt', 'r') as f:
    lines = f.read().strip().split('\n')
    enc_example = bytes.fromhex(lines[0])
    enc_flag = bytes.fromhex(lines[1])

known_pt = b"example"

# Get the deterministic parts
myrandom = random.Random(42)
k1 = myrandom.randbytes(8)
choices = list(myrandom.randbytes(6))  # Same for both calls

print(f"k1 = k3: {k1.hex()}")
print(f"choices: {choices}")

# The key insight: maybe the global random state is in its initial state
# Let's try to understand what the initial state produces

print("Testing different initial states...")

# Maybe the random module was imported but never seeded
# Let's see what happens with a fresh Python process
import subprocess
import sys

# Create a test script to see what random.randint produces in a fresh state
test_script = '''
import random
for i in range(16):
    print(random.randint(0, 3), end=" ")
print()
'''

result = subprocess.run([sys.executable, '-c', test_script], capture_output=True, text=True)
fresh_randoms = list(map(int, result.stdout.strip().split()))
print(f"Fresh Python random.randint(0,3) sequence: {fresh_randoms}")

# Now let's use this sequence to build k2 and k4
k2 = b''
k4 = b''

for i in range(8):
    k2 += bytes([choices[fresh_randoms[i]]])

for i in range(8, 16):
    k4 += bytes([choices[fresh_randoms[i]]])

print(f"k2: {k2.hex()}")
print(f"k4: {k4.hex()}")

try:
    decrypted = dec(enc_example, k1, k2, k1, k4)  # k3 = k1
    if decrypted == known_pt:
        print("SUCCESS with fresh Python state!")
        
        # Decrypt the flag
        flag = dec(enc_flag, k1, k2, k1, k4)
        print(f"Flag: {flag.decode()}")
    else:
        print(f"Decrypted: {decrypted}")
        print("Not the right plaintext")
except Exception as e:
    print(f"Decryption failed: {e}")

# Let's also try some other common patterns
print("\nTrying other patterns...")

# Pattern 1: All zeros
k2 = bytes([choices[0]] * 8)
k4 = bytes([choices[0]] * 8)
try:
    decrypted = dec(enc_example, k1, k2, k1, k4)
    if decrypted == known_pt:
        print("SUCCESS with all zeros!")
        flag = dec(enc_flag, k1, k2, k1, k4)
        print(f"Flag: {flag.decode()}")
except:
    pass

# Pattern 2: All threes  
k2 = bytes([choices[3]] * 8)
k4 = bytes([choices[3]] * 8)
try:
    decrypted = dec(enc_example, k1, k2, k1, k4)
    if decrypted == known_pt:
        print("SUCCESS with all threes!")
        flag = dec(enc_flag, k1, k2, k1, k4)
        print(f"Flag: {flag.decode()}")
except:
    pass

# Pattern 3: Alternating
k2 = bytes([choices[i % 4] for i in range(8)])
k4 = bytes([choices[i % 4] for i in range(8)])
try:
    decrypted = dec(enc_example, k1, k2, k1, k4)
    if decrypted == known_pt:
        print("SUCCESS with alternating!")
        flag = dec(enc_flag, k1, k2, k1, k4)
        print(f"Flag: {flag.decode()}")
except:
    pass

print("Pattern testing complete")
