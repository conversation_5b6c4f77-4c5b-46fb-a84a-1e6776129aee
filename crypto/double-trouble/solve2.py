from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad
import random

def dec(ct, k1, k2, k3, k4):
    key2 = k4+k3
    cipher = AES.new(key2, mode=AES.MODE_ECB)
    ct1 = cipher.decrypt(ct)
    key1 = k1+k2
    cipher = AES.new(key1, mode=AES.MODE_ECB)
    pt = cipher.decrypt(ct1)
    return unpad(pt, 16)

# Read the encrypted data
with open('out.txt', 'r') as f:
    lines = f.read().strip().split('\n')
    enc_example = bytes.fromhex(lines[0])
    enc_flag = bytes.fromhex(lines[1])

known_pt = b"example"

# Get the deterministic parts (always the same due to Random(42))
myrandom = random.Random(42)
k1 = myrandom.randbytes(8)
choices1 = list(myrandom.randbytes(6))

myrandom = random.Random(42)
k3 = myrandom.randbytes(8)
choices2 = list(myrandom.randbytes(6))

print(f"k1: {k1.hex()}")
print(f"k3: {k3.hex()}")
print(f"choices1: {choices1}")
print(f"choices2: {choices2}")

# Now we need to find the right k2 and k4
# k2 is built from 8 calls to random.randint(0,3) using choices1
# k4 is built from 8 calls to random.randint(0,3) using choices2
# The global random state is what we need to figure out

print("Brute forcing the global random state...")

# Let's try different seeds for the global random state
for seed in range(10000):
    if seed % 1000 == 0:
        print(f"Trying seed {seed}...")
    
    # Set the global random seed
    random.seed(seed)
    
    # Generate k2 (first gen() call)
    k2 = b''
    for _ in range(8):
        k2 += bytes([choices1[random.randint(0, 3)]])
    
    # Generate k4 (second gen() call)  
    k4 = b''
    for _ in range(8):
        k4 += bytes([choices2[random.randint(0, 3)]])
    
    try:
        decrypted = dec(enc_example, k1, k2, k3, k4)
        if decrypted == known_pt:
            print(f"SUCCESS with seed {seed}!")
            print(f"k1: {k1.hex()}")
            print(f"k2: {k2.hex()}")
            print(f"k3: {k3.hex()}")
            print(f"k4: {k4.hex()}")
            
            # Decrypt the flag
            flag = dec(enc_flag, k1, k2, k3, k4)
            print(f"Flag: {flag.decode()}")
            exit()
    except Exception as e:
        continue

print("Could not find the correct seed in range 0-9999")

# Maybe the global random was never seeded (uses system time)
# Let's try the current approach but with no seed
print("Trying with no explicit seed (system time based)...")

# This won't work since we can't reproduce the exact system time
# But let's try a few random states
import time

for i in range(100):
    # Use current time as seed (this is what Python does by default)
    current_time = int(time.time()) - i  # Try recent timestamps
    random.seed(current_time)
    
    # Generate k2 and k4
    k2 = b''
    for _ in range(8):
        k2 += bytes([choices1[random.randint(0, 3)]])
    
    k4 = b''
    for _ in range(8):
        k4 += bytes([choices2[random.randint(0, 3)]])
    
    try:
        decrypted = dec(enc_example, k1, k2, k3, k4)
        if decrypted == known_pt:
            print(f"SUCCESS with timestamp {current_time}!")
            print(f"k1: {k1.hex()}")
            print(f"k2: {k2.hex()}")
            print(f"k3: {k3.hex()}")
            print(f"k4: {k4.hex()}")
            
            # Decrypt the flag
            flag = dec(enc_flag, k1, k2, k3, k4)
            print(f"Flag: {flag.decode()}")
            exit()
    except:
        continue

print("Time-based approach also failed")
