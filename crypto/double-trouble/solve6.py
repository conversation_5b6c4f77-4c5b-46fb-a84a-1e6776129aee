from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad
import random
import itertools

# Read the encrypted data
with open('out.txt', 'r') as f:
    lines = f.read().strip().split('\n')
    enc_example = bytes.fromhex(lines[0])
    enc_flag = bytes.fromhex(lines[1])

known_pt = b"example"

# Get the deterministic parts
myrandom = random.Random(42)
k1 = myrandom.randbytes(8)
choices = list(myrandom.randbytes(6))

print(f"k1 = k3: {k1.hex()}")
print(f"choices: {choices}")

# Meet-in-the-middle attack
# Encrypt known plaintext with all possible first keys
# Decrypt ciphertext with all possible second keys
# Look for matches

print("Starting meet-in-the-middle attack...")

# Dictionary to store intermediate values
intermediate_values = {}

# Phase 1: Encrypt known plaintext with all possible first keys (k1+k2)
print("Phase 1: Computing all possible encryptions with first key...")
count = 0

for k2_indices in itertools.product(range(4), repeat=8):
    count += 1
    if count % 10000 == 0:
        print(f"Phase 1: {count}/65536")
    
    k2 = b''.join(bytes([choices[i]]) for i in k2_indices)
    key1 = k1 + k2
    
    cipher = AES.new(key1, mode=AES.MODE_ECB)
    intermediate = cipher.encrypt(pad(known_pt, 16))
    
    # Store the intermediate value with the key indices
    intermediate_values[intermediate] = k2_indices

print(f"Phase 1 complete. Stored {len(intermediate_values)} intermediate values.")

# Phase 2: Decrypt ciphertext with all possible second keys (k4+k3)
print("Phase 2: Trying all possible decryptions with second key...")
count = 0

for k4_indices in itertools.product(range(4), repeat=8):
    count += 1
    if count % 10000 == 0:
        print(f"Phase 2: {count}/65536")
    
    k4 = b''.join(bytes([choices[i]]) for i in k4_indices)
    key2 = k4 + k1  # k3 = k1
    
    cipher = AES.new(key2, mode=AES.MODE_ECB)
    intermediate = cipher.decrypt(enc_example)
    
    # Check if this intermediate value was seen in phase 1
    if intermediate in intermediate_values:
        k2_indices = intermediate_values[intermediate]
        k2 = b''.join(bytes([choices[i]]) for i in k2_indices)
        
        print(f"\nMATCH FOUND!")
        print(f"k1: {k1.hex()}")
        print(f"k2: {k2.hex()}")
        print(f"k3: {k1.hex()}")
        print(f"k4: {k4.hex()}")
        print(f"k2_indices: {k2_indices}")
        print(f"k4_indices: {k4_indices}")
        
        # Verify by full decryption
        def dec(ct, k1, k2, k3, k4):
            key2 = k4+k3
            cipher = AES.new(key2, mode=AES.MODE_ECB)
            ct1 = cipher.decrypt(ct)
            key1 = k1+k2
            cipher = AES.new(key1, mode=AES.MODE_ECB)
            pt = cipher.decrypt(ct1)
            return unpad(pt, 16)
        
        try:
            decrypted = dec(enc_example, k1, k2, k1, k4)
            if decrypted == known_pt:
                print("Verification successful!")
                
                # Decrypt the flag
                flag = dec(enc_flag, k1, k2, k1, k4)
                print(f"Flag: {flag.decode()}")
                exit()
            else:
                print(f"Verification failed. Got: {decrypted}")
        except Exception as e:
            print(f"Verification error: {e}")

print("Phase 2 complete. No matches found.")
print("This suggests there might be an error in the approach.")
