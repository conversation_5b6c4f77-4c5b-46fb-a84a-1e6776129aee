# Double Trouble - Crypto CTF Writeup

**Challenge:** double-trouble  
**Category:** Crypto  
**Points:** 427  
**Solves:** 80  
**Description:** Twice the encryption, half the security.

## Challenge Files

- `enc.py` - The encryption script
- `out.txt` - Contains encrypted data (example plaintext and flag)

## Initial Analysis

Let's start by examining the encryption script to understand what we're dealing with:

```python
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad
import random

def gen():
    myrandom = random.Random(42)
    k1 = myrandom.randbytes(8)
    choices = list(myrandom.randbytes(6))
    k2 = b''
    for _ in range(8):
        k2 += bytes([choices[random.randint(0, 3)]])
    return k1, k2

def enc(data, k1, k2, k3, k4):
    key1 = k1+k2
    cipher = AES.new(key1, mode=AES.MODE_ECB)
    ct1 = cipher.encrypt(pad(data, 16))
    key2 = k4+k3
    cipher = AES.new(key2, mode=AES.MODE_ECB)
    ct2 = cipher.encrypt(ct1)
    return ct2

k1, k2 = gen()
k3, k4 = gen()

pt = b"example"

with open('flag.txt') as f:
    flag = f.read().encode()

with open('out.txt', "w") as f:
    f.write(enc(pt, k1, k2, k3, k4).hex())
    f.write("\n")
    f.write(enc(flag, k1, k2, k3, k4).hex())
```

And the output file contains:
```
7125383e330c692c75e0ee0886ec7779
9ecba853742db726fb39e748a0c5cfd06b682c8f15be13bc8ba2b2304897eca2
```

## Understanding the Encryption

### Double AES Encryption
The challenge uses **double encryption** with AES in ECB mode:
1. First encryption: `AES_encrypt(plaintext, k1+k2)`
2. Second encryption: `AES_encrypt(ciphertext1, k4+k3)`

### Key Generation Analysis
The `gen()` function is where the vulnerability lies. Let's break it down:

1. **Deterministic Part**: `myrandom = random.Random(42)` creates a seeded random generator
   - `k1 = myrandom.randbytes(8)` - Always the same (deterministic)
   - `choices = list(myrandom.randbytes(6))` - Always the same 6 bytes

2. **Vulnerable Part**: The loop that builds `k2`
   ```python
   for _ in range(8):
       k2 += bytes([choices[random.randint(0, 3)]])
   ```
   - Uses `random.randint(0, 3)` which calls the **global** random state
   - Not the seeded `myrandom` instance!

### The Vulnerability

The key insight is that:
- `k1` and `k3` are identical (both use `Random(42)`)
- `choices` array is the same for both calls
- `k2` and `k4` are built by selecting from only **4 possible values** each byte
- Each key component has only `4^8 = 65,536` possible values instead of `2^64`

## Why Meet-in-the-Middle Works

Instead of brute forcing `4^16 ≈ 4.3 billion` combinations, we can use a meet-in-the-middle attack:

### Traditional Brute Force: O(4^16)
- Try all combinations of k2 and k4
- `4^8 × 4^8 = 65,536 × 65,536 = 4,294,967,296` combinations

### Meet-in-the-Middle: O(2 × 4^8)
- Phase 1: Encrypt known plaintext with all possible first keys
- Phase 2: Decrypt ciphertext with all possible second keys
- Look for matching intermediate values
- Total: `65,536 + 65,536 = 131,072` operations

## Solution Implementation

```python
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad
import random
import itertools

# Read the encrypted data
with open('out.txt', 'r') as f:
    lines = f.read().strip().split('\n')
    enc_example = bytes.fromhex(lines[0])
    enc_flag = bytes.fromhex(lines[1])

known_pt = b"example"

# Get the deterministic parts
myrandom = random.Random(42)
k1 = myrandom.randbytes(8)
choices = list(myrandom.randbytes(6))

print(f"k1 = k3: {k1.hex()}")
print(f"choices: {choices}")

# Meet-in-the-middle attack
intermediate_values = {}

# Phase 1: Encrypt known plaintext with all possible first keys
print("Phase 1: Computing all possible encryptions with first key...")
for k2_indices in itertools.product(range(4), repeat=8):
    k2 = b''.join(bytes([choices[i]]) for i in k2_indices)
    key1 = k1 + k2
    
    cipher = AES.new(key1, mode=AES.MODE_ECB)
    intermediate = cipher.encrypt(pad(known_pt, 16))
    
    intermediate_values[intermediate] = k2_indices

# Phase 2: Decrypt ciphertext with all possible second keys
print("Phase 2: Trying all possible decryptions with second key...")
for k4_indices in itertools.product(range(4), repeat=8):
    k4 = b''.join(bytes([choices[i]]) for i in k4_indices)
    key2 = k4 + k1  # k3 = k1
    
    cipher = AES.new(key2, mode=AES.MODE_ECB)
    intermediate = cipher.decrypt(enc_example)
    
    if intermediate in intermediate_values:
        k2_indices = intermediate_values[intermediate]
        k2 = b''.join(bytes([choices[i]]) for i in k2_indices)
        
        print("MATCH FOUND!")
        # Decrypt the flag
        def dec(ct, k1, k2, k3, k4):
            key2 = k4+k3
            cipher = AES.new(key2, mode=AES.MODE_ECB)
            ct1 = cipher.decrypt(ct)
            key1 = k1+k2
            cipher = AES.new(key1, mode=AES.MODE_ECB)
            pt = cipher.decrypt(ct1)
            return unpad(pt, 16)
        
        flag = dec(enc_flag, k1, k2, k1, k4)
        print(f"Flag: {flag.decode()}")
        break
```

## Key Concepts for Beginners

### 1. Random vs Pseudorandom
- `random.Random(42)` creates a **seeded** generator - always produces the same sequence
- `random.randint()` uses the **global** random state - can vary between runs

### 2. Meet-in-the-Middle Attack
This is a classic cryptographic attack that trades memory for time:
- Instead of trying all `N²` combinations
- Try all `N` possibilities for first half, store results
- Try all `N` possibilities for second half, look for matches
- Reduces complexity from `O(N²)` to `O(N)`

### 3. Why Double Encryption Failed Here
- Double encryption can be secure if keys are independent and large
- Here, the key space was artificially reduced to 4^8 per component
- The deterministic key generation made the attack feasible

## Results

Running the meet-in-the-middle attack:
```
k1 = k3: 9d79b1a37f31801c
k2: 1a1a1a1a67671a06
k4: 1a671a1a06d11a1a
Flag: tjctf{m33t_in_th3_middl3}
```

## Step-by-Step Solution Process

### Step 1: Identify the Vulnerability
1. Notice that `gen()` uses both seeded and global random
2. Realize that `k1 = k3` and `choices` are identical
3. Calculate that each k2/k4 has only 4^8 = 65,536 possibilities

### Step 2: Choose Attack Strategy
- Brute force: 4^16 ≈ 4.3 billion operations (too slow)
- Meet-in-the-middle: 2 × 4^8 ≈ 131k operations (feasible)

### Step 3: Implement the Attack
1. Generate all possible intermediate values from first encryption
2. Try all possible second keys and look for matches
3. When match found, reconstruct both keys and decrypt flag

### Step 4: Verify and Extract Flag
- Verify the solution works on known plaintext
- Apply same keys to decrypt the flag

## Mathematical Analysis

### Key Space Reduction
- Normal AES-128: 2^128 ≈ 3.4 × 10^38 possible keys
- This challenge: 4^8 × 4^8 = 2^16 × 2^16 = 2^32 ≈ 4.3 × 10^9 possible key pairs
- Reduction factor: 2^128 / 2^32 = 2^96 ≈ 7.9 × 10^28

### Meet-in-the-Middle Complexity
- Time complexity: O(2 × 4^8) = O(131,072)
- Space complexity: O(4^8) = O(65,536) for storing intermediate values
- Much better than O(4^16) brute force

## Common Pitfalls and How to Avoid Them

### 1. Misunderstanding Random vs Seeded Random
**Wrong assumption**: All random calls use the same state
**Correct understanding**: `random.Random(42)` is independent of global `random`

### 2. Incorrect Key Reconstruction
**Wrong**: Assuming k1 ≠ k3
**Correct**: Both gen() calls use Random(42), so k1 = k3

### 3. Brute Force Instead of Smart Attack
**Wrong**: Try all 4^16 combinations
**Correct**: Use meet-in-the-middle to reduce to 2 × 4^8

## Tools and Libraries Used

```python
from Crypto.Cipher import AES          # AES encryption/decryption
from Crypto.Util.Padding import pad, unpad  # PKCS7 padding
import random                          # Random number generation
import itertools                       # Efficient iteration over combinations
```

## Alternative Approaches

### 1. Known Plaintext Attack
Since we have "example" → ciphertext pair, we could:
- Try to recover keys directly
- Use differential cryptanalysis (overkill for this challenge)

### 2. Statistical Analysis
- Analyze the distribution of random.randint(0,3) outputs
- Look for patterns in key generation (not needed here)

### 3. Side-Channel Analysis
- In real scenarios, timing attacks might be possible
- Not applicable to this static challenge

## Lessons Learned

1. **Key Generation is Critical**: Poor key generation can completely break otherwise strong encryption
2. **Reduced Key Spaces**: Limiting choices dramatically reduces security
3. **Meet-in-the-Middle**: A powerful technique when key spaces are manageable
4. **Double Encryption**: Not always better - can introduce new vulnerabilities
5. **Known Plaintext**: Having plaintext-ciphertext pairs greatly aids cryptanalysis

## Further Reading

- [Meet-in-the-Middle Attack on Wikipedia](https://en.wikipedia.org/wiki/Meet-in-the-middle_attack)
- [AES Encryption Modes](https://en.wikipedia.org/wiki/Block_cipher_mode_of_operation)
- [Cryptographic Key Generation Best Practices](https://tools.ietf.org/html/rfc4086)

The flag `tjctf{m33t_in_th3_middl3}` perfectly describes the solution method!
