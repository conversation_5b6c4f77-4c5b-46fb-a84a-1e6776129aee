#!/usr/bin/env python3

import time
import socket
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad

class RandomGenerator:
    def __init__(self, seed = None, modulus = 2 ** 32, multiplier = 157, increment = 1):
        if seed is None: 
            seed = time.asctime()
        if type(seed) is int: 
            self.seed = seed
        if type(seed) is str: 
            self.seed = int.from_bytes(seed.encode(), "big")
        if type(seed) is bytes: 
            self.seed = int.from_bytes(seed, "big")
        self.m = modulus
        self.a = multiplier
        self.c = increment

    def randint(self, bits: int):
        self.seed = (self.a * self.seed + self.c) % self.m
        result = self.seed.to_bytes(4, "big")
        while len(result) < bits // 8:
            self.seed = (self.a * self.seed + self.c) % self.m
            result += self.seed.to_bytes(4, "big")
        return int.from_bytes(result, "big") % (2 ** bits)

    def randbytes(self, len: int):
        return self.randint(len * 8).to_bytes(len, "big")

def connect_and_get_flag_ciphertext():
    """Connect to the service and get the flag ciphertext"""
    s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    s.connect(('tjc.tf', 31493))

    # Read welcome message
    data = s.recv(1024).decode()
    print("Received:", repr(data))

    # Read more data to get the ciphertext
    time.sleep(0.5)  # Give server time to send ciphertext
    data += s.recv(4096).decode()
    print("Full data:", repr(data))

    # Extract ciphertext from the response
    lines = data.strip().split('\n')
    for line in lines:
        if 'ciphertext = ' in line:
            # Parse the ciphertext
            ciphertext_str = line.split('ciphertext = ')[1]
            # Convert string representation back to bytes
            ciphertext = eval(ciphertext_str)
            return ciphertext, s

    return None, s

def get_encryption_oracle(s, plaintext):
    """Send plaintext to encryption oracle and get ciphertext"""
    s.send(plaintext + b'\n')
    response = s.recv(4096).decode()
    print(f"Oracle response: {response}")
    
    # Extract ciphertext
    for line in response.split('\n'):
        if 'ciphertext = ' in line:
            ciphertext_str = line.split('ciphertext = ')[1]
            return eval(ciphertext_str)
    return None

def brute_force_time_seed(flag_ciphertext, oracle_socket):
    """
    Try to brute force the time seed by testing different time values
    around the current time
    """
    current_time = time.time()
    
    # Try times around when we connected (within a reasonable window)
    for time_offset in range(-60, 61):  # Try ±60 seconds
        test_time = current_time + time_offset
        time_str = time.asctime(time.localtime(test_time))
        
        if time_offset % 10 == 0:  # Print progress every 10 attempts
            print(f"Trying time seed: {time_str}")

        # Create RNG with this time seed
        rng = RandomGenerator(time_str)

        # Generate the key that would have been used for the flag
        flag_key = rng.randbytes(32)

        # Try to decrypt the flag
        try:
            cipher = AES.new(flag_key, AES.MODE_ECB)
            decrypted = cipher.decrypt(flag_ciphertext)
            unpadded = unpad(decrypted, AES.block_size)

            # Check if it looks like a flag
            if b'tjctf{' in unpadded or b'flag' in unpadded.lower():
                print(f"Found flag with time seed {time_str}:")
                print(f"Flag: {unpadded.decode().strip()}")
                return unpadded
                
        except Exception as e:
            continue
    
    return None

def main():
    print("Connecting to the service...")
    flag_ciphertext, s = connect_and_get_flag_ciphertext()
    
    if not flag_ciphertext:
        print("Failed to get flag ciphertext")
        return
    
    print(f"Flag ciphertext: {flag_ciphertext}")
    print(f"Length: {len(flag_ciphertext)} bytes")
    
    # Try to brute force the time seed
    flag = brute_force_time_seed(flag_ciphertext, s)
    
    if flag:
        print(f"Successfully recovered flag: {flag}")
    else:
        print("Failed to recover flag")
    
    # Clean up
    s.send(b'quit\n')
    s.close()

if __name__ == "__main__":
    main()
