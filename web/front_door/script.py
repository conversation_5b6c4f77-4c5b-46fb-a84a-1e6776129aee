import base64

def hash_char(hash_char, key_char):
    return chr(pow(ord(hash_char), ord(key_char), 26) + 65)

def has(inp, key):
    hashed = ""
    for i in range(64):
        c = inp[i % len(inp)]
        k = key[i % len(key)]
        hashed += hash_char(c, k)
    return hashed

def recover_key_char(hash_char, output_char):
    for i in range(32, 127):  # printable ASCII range
        if chr(pow(ord(hash_char), i, 26) + 65) == output_char:
            return chr(i)
    return None

def recover_key(known_input, known_hash):
    key_chars = []
    for i in range(64):
        c = known_input[i % len(known_input)]
        o = known_hash[i]
        k = recover_key_char(c, o)
        if k is None:
            print(f"[!] Failed to recover key char at position {i} (input '{c}' output '{o}')")
            return None
        key_chars.append(k)
    return "".join(key_chars)

def base64url_encode(data: bytes) -> str:
    return base64.urlsafe_b64encode(data).decode().rstrip("=")

def forge_token(key, payload_json, header_json='{"alg": "ADMINHASH", "typ": "JWT"}'):
    header_b64 = base64url_encode(header_json.encode())
    payload_b64 = base64url_encode(payload_json.encode())
    signature = has(payload_json, key)
    return f"{header_b64}.{payload_b64}.{signature}"

if __name__ == "__main__":
    # Your known test token payload & signature
    known_payload = '{"username": "test", "password": "test", "admin": "false"}'
    known_signature = "JZOAYHBBBBNBDDQABXBFJOABZBLBBSOBVLBWVBQRSJJBOJYXDQZBEIRQBSOOFFWB"

    print("[*] Recovering key from known payload and signature...")
    jwt_key = recover_key(known_payload, known_signature)
    if jwt_key is None:
        print("[!] Key recovery failed. Please check input strings exactly.")
        exit(1)
    print(f"[*] Recovered jwt_key: {jwt_key}")

    # Payload to forge with admin privileges
    admin_payload = '{"username":"admin","password":"admin","admin":"true"}'

    print("[*] Forging admin token...")
    forged_token = forge_token(jwt_key, admin_payload)
    print("\n=== FORGED ADMIN TOKEN ===")
    print(forged_token)
