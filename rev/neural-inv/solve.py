#!/usr/bin/env python3
import numpy as np

def sigmoid_inverse(y):
    """Inverse of sigmoid function"""
    y = np.clip(y, 1e-15, 1-1e-15)
    return np.log(y / (1 - y))

def invert_neural_network():
    """Invert 2-layer sigmoid neural network to recover input"""
    # Load model
    model = np.load("model.npz")
    W1, b1, W2, b2, y = model["W1"], model["b1"], model["W2"], model["b2"], model["y"]

    # Step 1: Invert final sigmoid layer
    z2 = sigmoid_inverse(y)

    # Step 2: Solve for hidden layer: h1 = W2^(-1) @ (z2 - b2)
    h1 = np.linalg.inv(W2) @ (z2 - b2)

    # Step 3: Invert hidden sigmoid layer
    h1_clipped = np.clip(h1, 1e-15, 1-1e-15)
    z1 = sigmoid_inverse(h1_clipped)

    # Step 4: Solve for input: x = W1^(-1) @ (z1 - b1)
    x = np.linalg.inv(W1) @ (z1 - b1)

    return x

def extract_flag(x):
    """Extract ASCII flag from recovered input"""
    # Scale by 127 and convert to ASCII
    ascii_vals = np.round(x * 127).astype(int)
    flag = ''.join([chr(c) for c in ascii_vals])
    return flag

if __name__ == "__main__":
    # Invert the neural network
    x = invert_neural_network()

    # Extract the flag
    flag = extract_flag(x)
    print(f"Flag: {flag}")
