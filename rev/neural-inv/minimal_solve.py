#!/usr/bin/env python3
import numpy as np

# Load the model data
model = np.load("model.npz")
W1, b1, W2, b2, y = model["W1"], model["b1"], model["W2"], model["b2"], model["y"]

# Step 1: z2 = σ⁻¹(y) [Invert final sigmoid]
y_clipped = np.clip(y, 1e-15, 1-1e-15)  # Avoid numerical issues
z2 = np.log(y_clipped / (1 - y_clipped))

# Step 2: h1 = W2⁻¹(z2 - b2) [Solve linear system]
h1 = np.linalg.inv(W2) @ (z2 - b2)

# Step 3: z1 = σ⁻¹(h1) [Invert hidden sigmoid]
h1_clipped = np.clip(h1, 1e-15, 1-1e-15)  # Avoid numerical issues
z1 = np.log(h1_clipped / (1 - h1_clipped))

# Step 4: x = W1⁻¹(z1 - b1) [Recover original input]
x = np.linalg.inv(W1) @ (z1 - b1)

# Convert to flag: x * 127 → ASCII
print(f"Raw x values: {x}")
ascii_vals = np.round(x * 127).astype(int)
print(f"ASCII values: {ascii_vals}")
flag = ''.join([chr(val) for val in ascii_vals])
print(f"Flag: {flag}")
