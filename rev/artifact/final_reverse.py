#!/usr/bin/env python3
"""
Final reverse solution - try to find the exact timing
"""

import math
import ast

# Constants from main.py
circ = 24901
diam = (circ / (2 * math.pi)) ** 2
rad = math.sqrt(diam)
spin = 1000

def reverse_char(point, time):
    """Reverse a single character"""
    try:
        # Step 1: Get the post-spin angle
        angle = math.asin(point / rad)
        
        # Step 2: Calculate the spin angle in degrees
        spin_deg = (time / circ) * 360
        
        # Step 3: Remove the spin to get original angle
        orig_angle_deg = math.degrees(angle) - spin_deg
        orig_angle = math.radians(orig_angle_deg)
        
        # Step 4: Get the original point
        orig_point = math.sin(orig_angle) * rad
        
        # Step 5: Solve for the ASCII character
        char_val = math.sqrt(diam - orig_point ** 2) / 31
        char_code = int(round(char_val))
        
        if 0 <= char_code <= 127:
            return chr(char_code)
        else:
            return None
    except:
        return None

def find_best_timing_for_point(point, target_chars=None):
    """Find the best timing value for a point"""
    best_results = []
    
    # Try a wide range of timing values
    for time in range(10, 150):
        char = reverse_char(point, time)
        if char and 32 <= ord(char) <= 126:  # Printable ASCII
            best_results.append((time, char, ord(char)))
    
    return best_results

def main():
    print("=== Final Reverse Solution ===")
    
    # Read the points
    with open("points.txt", 'r') as f:
        content = f.read().strip()
        points = ast.literal_eval(content)
    
    print(f"Loaded {len(points)} points")
    
    # For each point, find all possible characters with different timings
    print("\nFinding possible characters for each point:")
    
    all_possibilities = []
    for i, point in enumerate(points):
        possibilities = find_best_timing_for_point(point)
        all_possibilities.append(possibilities)
        
        print(f"Point {i:2d} ({point:8.2f}): ", end="")
        if possibilities:
            # Show first few possibilities
            for j, (time, char, ascii_val) in enumerate(possibilities[:5]):
                print(f"'{char}'({time})", end=" ")
            if len(possibilities) > 5:
                print("...", end="")
        else:
            print("no matches", end="")
        print()
    
    # Try to construct a flag by picking the most reasonable characters
    print(f"\n=== Constructing Flag ===")
    
    # Strategy 1: Pick the first printable character for each position
    flag1 = ""
    for i, possibilities in enumerate(all_possibilities):
        if possibilities:
            char = possibilities[0][1]  # First possibility
            flag1 += char
        else:
            flag1 += "?"
    
    print(f"Strategy 1 (first choice): {flag1}")
    
    # Strategy 2: Look for flag-like patterns
    # Try to find combinations that look like tjctf{...}
    flag2 = ""
    for i, possibilities in enumerate(all_possibilities):
        if i == 0:  # First char should be 't'
            best_char = 't' if any(p[1] == 't' for p in possibilities) else (possibilities[0][1] if possibilities else '?')
        elif i == 1:  # Second char should be 'j'
            best_char = 'j' if any(p[1] == 'j' for p in possibilities) else (possibilities[0][1] if possibilities else '?')
        elif i == 2:  # Third char should be 'c'
            best_char = 'c' if any(p[1] == 'c' for p in possibilities) else (possibilities[0][1] if possibilities else '?')
        elif i == 3:  # Fourth char should be 't'
            best_char = 't' if any(p[1] == 't' for p in possibilities) else (possibilities[0][1] if possibilities else '?')
        elif i == 4:  # Fifth char should be 'f'
            best_char = 'f' if any(p[1] == 'f' for p in possibilities) else (possibilities[0][1] if possibilities else '?')
        elif i == 5:  # Sixth char should be '{'
            best_char = '{' if any(p[1] == '{' for p in possibilities) else (possibilities[0][1] if possibilities else '?')
        else:
            # For other positions, pick the most common printable character
            if possibilities:
                best_char = possibilities[0][1]
            else:
                best_char = '?'
        
        flag2 += best_char
    
    print(f"Strategy 2 (flag format): {flag2}")
    
    # Strategy 3: Use the simulated timing approach but with better timing
    print(f"\n=== Strategy 3: Refined timing ===")
    
    # Use a more consistent timing value based on the fake flag timings
    fake_flag_times = [70, 72, 76, 75, 71, 80, 95, 77, 88, 71, 80, 90, 83, 97, 79, 71]
    avg_time = sum(fake_flag_times) / len(fake_flag_times)
    print(f"Average timing from fake flag: {avg_time:.1f}")
    
    flag3 = ""
    for i, point in enumerate(points):
        # Try timing values around the average
        best_char = None
        best_score = float('inf')
        
        for time_offset in range(-20, 21, 5):
            test_time = int(avg_time + time_offset)
            char = reverse_char(point, test_time)
            if char and 32 <= ord(char) <= 126:
                # Score based on how common the character is in flags
                score = abs(time_offset)  # Prefer times closer to average
                if char.isalnum() or char in '_{}':  # Common flag characters
                    score -= 10
                if score < best_score:
                    best_score = score
                    best_char = char
        
        flag3 += best_char if best_char else '?'
    
    print(f"Strategy 3 (refined timing): {flag3}")
    
    print(f"\n=== FINAL RESULTS ===")
    print(f"Strategy 1: {flag1}")
    print(f"Strategy 2: {flag2}")
    print(f"Strategy 3: {flag3}")

if __name__ == "__main__":
    main()
