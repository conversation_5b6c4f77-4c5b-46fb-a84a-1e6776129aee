#!/usr/bin/env python3
"""
Systematic solution - try the most consistent characters
"""

import math
import ast

# Constants from main.py
circ = 24901
diam = (circ / (2 * math.pi)) ** 2
rad = math.sqrt(diam)
spin = 1000

def reverse_char(point, time):
    """Reverse a single character"""
    try:
        angle = math.asin(point / rad)
        spin_deg = (time / circ) * 360
        orig_angle_deg = math.degrees(angle) - spin_deg
        orig_angle = math.radians(orig_angle_deg)
        orig_point = math.sin(orig_angle) * rad
        char_val = math.sqrt(diam - orig_point ** 2) / 31
        char_code = int(round(char_val))
        
        if 0 <= char_code <= 127:
            return chr(char_code)
        else:
            return None
    except:
        return None

def main():
    print("=== Systematic Solution ===")
    
    # Read the points
    with open("points.txt", 'r') as f:
        content = f.read().strip()
        points = ast.literal_eval(content)
    
    # Based on my analysis, let me try the most promising approach
    # Use timing values that give the most consistent results
    
    # From the previous runs, I noticed that certain timing values work better
    # Let me try a few specific timing patterns
    
    timing_approaches = [
        # Approach 1: Low timing values (10-20 range)
        [15] * len(points),
        
        # Approach 2: Medium timing values (around 30-40)
        [35] * len(points),
        
        # Approach 3: Higher timing values (around 80 like fake flag average)
        [80] * len(points),
        
        # Approach 4: Variable timing based on position
        [20 + (i % 10) * 5 for i in range(len(points))],
    ]
    
    results = []
    
    for approach_num, times in enumerate(timing_approaches, 1):
        print(f"\n=== Approach {approach_num} ===")
        flag = ""
        
        for i, (point, time) in enumerate(zip(points, times)):
            char = reverse_char(point, time)
            if char and 32 <= ord(char) <= 126:
                flag += char
                print(f"Point {i:2d}: {point:8.2f} -> '{char}' (time {time})")
            else:
                flag += '?'
                print(f"Point {i:2d}: {point:8.2f} -> '?' (time {time})")
        
        results.append(flag)
        print(f"Result {approach_num}: {flag}")
    
    # Show all results
    print(f"\n=== ALL RESULTS ===")
    for i, result in enumerate(results, 1):
        print(f"Approach {i}: {result}")
    
    # Try to pick the best one
    print(f"\n=== ANALYSIS ===")
    
    # Look for the one that looks most like a flag
    best_flag = None
    best_score = -1
    
    for i, flag in enumerate(results, 1):
        score = 0
        
        # Score based on flag-like characteristics
        if flag.endswith('}'):
            score += 10
        
        # Count printable characters
        printable_count = sum(1 for c in flag if 32 <= ord(c) <= 126 and c != '?')
        score += printable_count
        
        # Penalize too many special characters
        special_count = sum(1 for c in flag if not c.isalnum() and c not in '_{}-')
        score -= special_count * 2
        
        print(f"Approach {i} score: {score} (printable: {printable_count}, special: {special_count})")
        
        if score > best_score:
            best_score = score
            best_flag = flag
    
    print(f"\nBest flag: {best_flag}")
    
    # Also try one more approach: use the exact timing from when I ran main.py earlier
    print(f"\n=== Using exact fake flag timing ===")
    fake_times = [70, 72, 76, 75, 71, 80, 95, 77, 88, 71, 80, 90, 83, 97, 79, 71]
    
    # Extend to cover all points
    extended_times = []
    for i in range(len(points)):
        extended_times.append(fake_times[i % len(fake_times)])
    
    exact_flag = ""
    for i, (point, time) in enumerate(zip(points, extended_times)):
        char = reverse_char(point, time)
        if char and 32 <= ord(char) <= 126:
            exact_flag += char
        else:
            exact_flag += '?'
    
    print(f"Exact timing flag: {exact_flag}")

if __name__ == "__main__":
    main()
