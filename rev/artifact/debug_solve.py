#!/usr/bin/env python3
"""
Debug the transformation step by step
"""

import ast
from time import perf_counter
from math import pi, sqrt, asin, degrees, radians, sin

# Constants from main.py
circ = 24901
diam = (circ / (2 * pi)) ** 2 
rad = sqrt(diam)
spin = 1000

def find_ang(point):
    return degrees(asin(point / rad))

def add_spin(points, time):
    ang = time / circ * 360
    for i in range(len(points)):
        nang = find_ang(points[i]) + ang
        points[i] = sin(radians(nang)) * rad

def test_transformation():
    """Test the transformation with known values"""
    print("=== Testing Transformation ===")
    
    # Test with a known character
    test_char = ord('t')  # 116
    print(f"Test character: 't' (ASCII {test_char})")
    
    # Calculate original point
    original_point = sqrt(diam - (test_char * 31) ** 2)
    print(f"Original point: {original_point:.6f}")
    
    # Test different time values
    for time_val in [30, 35, 40]:
        # Apply spin
        ang = time_val / circ * 360
        current_ang = find_ang(original_point)
        new_ang = current_ang + ang
        spun_point = sin(radians(new_ang)) * rad
        
        print(f"Time {time_val}: angle {ang:.6f}° -> spun point {spun_point:.6f}")
        
        # Try to reverse
        reverse_ang = find_ang(spun_point) - ang
        reversed_point = sin(radians(reverse_ang)) * rad
        
        print(f"  Reverse: {reversed_point:.6f} (diff: {abs(reversed_point - original_point):.6f})")
        
        # Convert back to character
        try:
            char_squared = (diam - reversed_point ** 2) / (31 ** 2)
            if char_squared >= 0:
                char_val = sqrt(char_squared)
                print(f"  Character: {char_val:.6f} -> {int(round(char_val))}")
            else:
                print(f"  Character: invalid (negative sqrt)")
        except Exception as e:
            print(f"  Character: error {e}")

def brute_force_single_point(shifted_point, position):
    """Brute force find the best character for a single point"""
    print(f"\n=== Brute Force Point {position}: {shifted_point:.6f} ===")
    
    best_matches = []
    
    # Try all ASCII characters
    for char_val in range(32, 127):
        try:
            original_point = sqrt(diam - (char_val * 31) ** 2)
            
            # Try different time values
            for time_val in range(10, 80):
                ang = time_val / circ * 360
                current_ang = find_ang(original_point)
                new_ang = current_ang + ang
                spun_point = sin(radians(new_ang)) * rad
                
                diff = abs(spun_point - shifted_point)
                if diff < 1.0:  # Very close matches only
                    best_matches.append((char_val, chr(char_val), time_val, diff))
        except:
            continue
    
    # Sort by difference
    best_matches.sort(key=lambda x: x[3])
    
    print(f"Best matches for point {position}:")
    for i, (char_val, char, time_val, diff) in enumerate(best_matches[:3]):  # Show only top 3
        print(f"  {i+1:2d}. '{char}' (ASCII {char_val:3d}) time {time_val:2d} diff {diff:.6f}")
    
    return best_matches[0] if best_matches else None

def main():
    # Read the shifted points
    with open("points.txt", 'r') as f:
        shifted_points = ast.literal_eval(f.read())
    
    # Test the transformation first
    test_transformation()
    
    # Brute force the first few points to understand the pattern
    print("\n" + "="*60)
    print("BRUTE FORCE ANALYSIS")
    print("="*60)
    
    flag_chars = []
    
    for i in range(len(shifted_points)):  # All points
        best_match = brute_force_single_point(shifted_points[i], i)
        if best_match:
            char_val, char, time_val, diff = best_match
            flag_chars.append(char)
            print(f"Position {i}: BEST = '{char}'")
        else:
            flag_chars.append('?')
            print(f"Position {i}: BEST = '?'")
    
    complete_flag = ''.join(flag_chars)
    print(f"\nComplete flag: {complete_flag}")

if __name__ == "__main__":
    main()
