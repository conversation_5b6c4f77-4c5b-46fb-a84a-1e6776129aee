#!/usr/bin/env python3
"""
Mathematical solution following the exact approach described
"""

import math
import ast

# Constants from main.py
circ = 24901
diam = (circ / (2 * math.pi)) ** 2
rad = math.sqrt(diam)
spin = 1000

def recover_char_from_point(point, time):
    """
    Recover character from point using exact mathematical reversal
    
    Given:
    - point: the final value after rotation
    - time: the timing value used for rotation
    
    Process:
    1. point = sin(radians(nang)) * rad
    2. nang = find_ang(p0) + ang
    3. ang = time / circ * 360
    4. find_ang(p0) = degrees(asin(p0 / rad))
    5. p0 = sqrt(diam - (i * 31)^2)
    
    Reverse:
    1. nang = degrees(asin(point / rad))
    2. ang = time / circ * 360
    3. original_ang = nang - ang
    4. p0 = rad * sin(radians(original_ang))
    5. i = sqrt((diam - p0^2)) / 31
    """
    try:
        # Step 1: Get nang from point
        nang = math.degrees(math.asin(point / rad))
        
        # Step 2: Calculate rotation angle
        ang = time / circ * 360
        
        # Step 3: Remove rotation to get original angle
        original_ang = nang - ang
        
        # Step 4: Get original point p0
        p0 = rad * math.sin(math.radians(original_ang))
        
        # Step 5: Solve for character
        # p0 = sqrt(diam - (i * 31)^2)
        # p0^2 = diam - (i * 31)^2
        # (i * 31)^2 = diam - p0^2
        # i * 31 = sqrt(diam - p0^2)
        # i = sqrt(diam - p0^2) / 31
        
        char_times_31_squared = diam - p0 * p0
        if char_times_31_squared >= 0:
            char_times_31 = math.sqrt(char_times_31_squared)
            char_val = char_times_31 / 31
            char_code = int(round(char_val))
            
            if 0 <= char_code <= 127:
                return chr(char_code), char_code
            else:
                return None, char_code
        else:
            return None, None
            
    except Exception as e:
        return None, None

def main():
    print("=== Mathematical Solution ===")
    
    # Read the points
    with open("points.txt", 'r') as f:
        content = f.read().strip()
        points = ast.literal_eval(content)
    
    print(f"Loaded {len(points)} points")
    
    # Read the timing values from the fake flag run
    fake_times = [64, 56, 56, 55, 55, 55, 71, 57, 58, 63, 66, 69, 68, 59, 70, 68]
    print(f"Fake flag timing values: {fake_times}")
    print(f"Average timing: {sum(fake_times)/len(fake_times):.1f}")
    
    # Since we have 35 points but only 16 timing values, we need to figure out
    # what timing was used for the real flag
    
    # Strategy 1: Assume the real flag used similar timing values
    # Extend the pattern cyclically
    extended_times = []
    for i in range(len(points)):
        extended_times.append(fake_times[i % len(fake_times)])
    
    print(f"\n=== Strategy 1: Cyclic timing pattern ===")
    flag1 = ""
    for i, (point, time) in enumerate(zip(points, extended_times)):
        char, char_code = recover_char_from_point(point, time)
        if char:
            flag1 += char
            print(f"Point {i:2d}: {point:8.2f} -> '{char}' (ASCII {char_code}) [time {time}]")
        else:
            flag1 += '?'
            print(f"Point {i:2d}: {point:8.2f} -> invalid (code {char_code}) [time {time}]")
    
    print(f"Strategy 1 result: {flag1}")
    
    # Strategy 2: Use average timing for all characters
    avg_time = sum(fake_times) / len(fake_times)
    print(f"\n=== Strategy 2: Average timing ({avg_time:.1f}) ===")
    flag2 = ""
    for i, point in enumerate(points):
        char, char_code = recover_char_from_point(point, avg_time)
        if char:
            flag2 += char
            print(f"Point {i:2d}: {point:8.2f} -> '{char}' (ASCII {char_code}) [time {avg_time:.1f}]")
        else:
            flag2 += '?'
            print(f"Point {i:2d}: {point:8.2f} -> invalid (code {char_code}) [time {avg_time:.1f}]")
    
    print(f"Strategy 2 result: {flag2}")
    
    # Strategy 3: Try to find the best timing for each point individually
    print(f"\n=== Strategy 3: Optimize timing per point ===")
    flag3 = ""
    for i, point in enumerate(points):
        best_char = None
        best_time = None
        best_score = -1
        
        # Try timing values in a reasonable range
        for test_time in range(30, 100):
            char, char_code = recover_char_from_point(point, test_time)
            if char and 32 <= ord(char) <= 126:  # Printable ASCII
                # Score based on how "flag-like" the character is
                score = 1
                if char.isalnum():
                    score += 2
                if char in 'tjctf{}':
                    score += 5
                if char in '_-':
                    score += 1
                
                if score > best_score:
                    best_score = score
                    best_char = char
                    best_time = test_time
        
        if best_char:
            flag3 += best_char
            print(f"Point {i:2d}: {point:8.2f} -> '{best_char}' [time {best_time}]")
        else:
            flag3 += '?'
            print(f"Point {i:2d}: {point:8.2f} -> no good match")
    
    print(f"Strategy 3 result: {flag3}")
    
    # Show all results
    print(f"\n=== FINAL RESULTS ===")
    print(f"Strategy 1 (cyclic timing):  {flag1}")
    print(f"Strategy 2 (average timing): {flag2}")
    print(f"Strategy 3 (optimized):      {flag3}")

if __name__ == "__main__":
    main()
