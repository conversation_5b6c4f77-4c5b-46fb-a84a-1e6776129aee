#!/usr/bin/env python3
"""
CTF Solution for rev/artifact challenge

The challenge:
1. Takes a flag and converts each character to a point using sqrt(diam - (char * 31)^2)
2. Each point gets "spun" based on timing, adding an angle to simulate shifting
3. We need to reverse this process to recover the original flag

Strategy:
1. Read the shifted points from points.txt
2. Simulate the original timing to get the spin amounts
3. Reverse the spinning transformation
4. Convert points back to ASCII characters
"""

import ast
from time import perf_counter
from math import pi, sqrt, asin, degrees, radians, sin

# Constants from main.py
circ = 24901
diam = (circ / (2 * pi)) ** 2 
rad = sqrt(diam)
spin = 1000

def find_ang(point):
    """Find angle from point (same as main.py)"""
    return degrees(asin(point / rad))

def reverse_spin(point, time):
    """Reverse the spinning transformation"""
    ang = time / circ * 360
    current_ang = find_ang(point)
    original_ang = current_ang - ang
    return sin(radians(original_ang)) * rad

def point_to_char(point):
    """Convert a point back to ASCII character"""
    # Original formula: point = sqrt(diam - (char * 31)^2)
    # Solving for char: char = sqrt(diam - point^2) / 31
    try:
        char_val = sqrt(diam - point ** 2) / 31
        return int(round(char_val))
    except:
        return 0

def simulate_timing(num_chars):
    """Simulate the timing from the original process"""
    times = []
    for i in range(num_chars):
        st = perf_counter()
        # Simulate the same loop as in main.py
        u = 0
        while u < 1000000:
            u += 1
        en = perf_counter()
        time = (en - st) * spin
        times.append(int(time))
    return times

def brute_force_approach(shifted_points):
    """Try to brute force the solution by testing different approaches"""
    print("\n=== Brute Force Approach ===")

    # Let's try to understand the pattern better
    # First, let's see what characters would give us points in the right range
    print("Analyzing point ranges...")

    for i, point in enumerate(shifted_points[:10]):
        print(f"Point {i}: {point:.2f}")
        # Try different character values to see what original point would make sense
        for char_val in range(32, 127):
            expected_point = sqrt(diam - (char_val * 31) ** 2)
            if abs(expected_point - point) < 200:  # Within reasonable range
                print(f"  Char {char_val} ('{chr(char_val)}') -> point {expected_point:.2f} (diff: {abs(expected_point - point):.2f})")

def analyze_transformation():
    """Analyze the transformation more carefully"""
    print("\n=== Transformation Analysis ===")

    # Let's understand what the add_spin function actually does
    print("Understanding add_spin transformation...")

    # Test with a known character
    test_char = ord('t')  # 116
    original_point = sqrt(diam - (test_char * 31) ** 2)
    print(f"Test char 't' ({test_char}) -> original point: {original_point:.2f}")

    # Apply different spin amounts
    for time_val in [30, 35, 40]:
        ang = time_val / circ * 360
        current_ang = find_ang(original_point)
        new_ang = current_ang + ang
        spun_point = sin(radians(new_ang)) * rad
        print(f"  Time {time_val} -> angle {ang:.2f} -> spun point: {spun_point:.2f}")

def main():
    print("=== CTF Artifact Solver ===")

    # Read the shifted points
    with open("points.txt", 'r') as f:
        shifted_points = ast.literal_eval(f.read())

    print(f"Found {len(shifted_points)} shifted points")

    # First, let's analyze the transformation
    analyze_transformation()

    # Try brute force approach for first few points
    brute_force_approach(shifted_points)

    # Now try the reverse approach with better understanding
    print("\n=== Improved Reverse Approach ===")

    # The key insight: we need to find what original points, when spun by various amounts,
    # would result in our shifted points

    flag_chars = []

    for i, shifted_point in enumerate(shifted_points):
        best_char = None
        best_diff = float('inf')
        best_time = None

        # Try all possible characters
        for char_val in range(32, 127):
            try:
                original_point = sqrt(diam - (char_val * 31) ** 2)

                # Try different reasonable time values (based on our simulation)
                for time_val in range(15, 60):  # Expanded range
                    ang = time_val / circ * 360
                    current_ang = find_ang(original_point)
                    new_ang = current_ang + ang
                    spun_point = sin(radians(new_ang)) * rad

                    diff = abs(spun_point - shifted_point)
                    if diff < best_diff:
                        best_diff = diff
                        best_char = char_val
                        best_time = time_val
            except:
                continue  # Skip invalid calculations

        if best_char and best_diff < 15:  # More lenient threshold
            flag_chars.append(chr(best_char))
            print(f"Point {i:2d}: {shifted_point:8.2f} -> char {best_char:3d} ('{chr(best_char)}') diff: {best_diff:.2f} time: {best_time}")
        else:
            # For problematic points, make educated guesses based on context
            if i == 0:  # First character, likely 't'
                flag_chars.append('t')
                print(f"Point {i:2d}: {shifted_point:8.2f} -> char 116 ('t') [GUESSED - start of tjctf]")
            elif i == 1:  # Second character, likely 'j'
                flag_chars.append('j')
                print(f"Point {i:2d}: {shifted_point:8.2f} -> char 106 ('j') [GUESSED - tjctf]")
            elif i == 2:  # Third character, likely 'c'
                flag_chars.append('c')
                print(f"Point {i:2d}: {shifted_point:8.2f} -> char  99 ('c') [GUESSED - tjctf]")
            elif i == 3:  # Fourth character, likely 't'
                flag_chars.append('t')
                print(f"Point {i:2d}: {shifted_point:8.2f} -> char 116 ('t') [GUESSED - tjctf]")
            elif i == 4:  # Fifth character, likely 'f'
                flag_chars.append('f')
                print(f"Point {i:2d}: {shifted_point:8.2f} -> char 102 ('f') [GUESSED - tjctf]")
            elif i == 5:  # Sixth character, likely '{'
                flag_chars.append('{')
                print(f"Point {i:2d}: {shifted_point:8.2f} -> char 123 ('{{') [GUESSED - opening brace]")
            else:
                flag_chars.append('?')
                print(f"Point {i:2d}: {shifted_point:8.2f} -> no good match (best diff: {best_diff:.2f})")

    flag_string = ''.join(flag_chars)
    print(f"\nReconstructed flag: {flag_string}")

    # Let's also try a more targeted approach for the known flag format
    print("\n=== Targeted Flag Format Approach ===")

    # We know it should be tjctf{...}
    expected_start = "tjctf{"
    expected_end = "}"

    # Let's verify our guesses by checking if they make sense
    print("Verifying flag format...")

    # Try to reconstruct with known prefix
    alt_flag_chars = list(expected_start)

    # For the middle part, use our best guesses
    for i in range(len(expected_start), len(shifted_points) - 1):
        if i < len(flag_chars):
            if flag_chars[i] != '?':
                alt_flag_chars.append(flag_chars[i])
            else:
                # Try common flag characters
                alt_flag_chars.append('_')

    alt_flag_chars.append(expected_end)
    alt_flag = ''.join(alt_flag_chars)
    print(f"Alternative with known format: {alt_flag}")

if __name__ == "__main__":
    main()
