#!/usr/bin/env python3
"""
Clean solution - understand the math step by step
"""

import ast
from time import perf_counter
from math import pi, sqrt, asin, degrees, radians, sin, cos

# Constants from main.py
circ = 24901
diam = (circ / (2 * pi)) ** 2 
rad = sqrt(diam)
spin = 1000

def find_ang(point):
    """Convert point to angle"""
    return degrees(asin(point / rad))

def char_to_point(char_val):
    """Convert character to original point"""
    return sqrt(diam - (char_val * 31) ** 2)

def point_to_char(point):
    """Convert point back to character"""
    try:
        char_val_squared = (diam - point ** 2) / (31 ** 2)
        if char_val_squared >= 0:
            return int(round(sqrt(char_val_squared)))
        return None
    except:
        return None

def apply_spin(point, time):
    """Apply spin transformation (same as add_spin but for single point)"""
    ang = time / circ * 360
    current_ang = find_ang(point)
    new_ang = current_ang + ang
    return sin(radians(new_ang)) * rad

def reverse_spin(spun_point, time):
    """Reverse the spin transformation"""
    ang = time / circ * 360
    current_ang = find_ang(spun_point)
    original_ang = current_ang - ang
    return sin(radians(original_ang)) * rad

def test_math():
    """Test the mathematical transformations"""
    print("=== Testing Mathematical Transformations ===")
    
    # Test with known character 't' = 116
    test_char = 116
    print(f"Test character: {test_char} ('{chr(test_char)}')")
    
    # Convert to point
    original_point = char_to_point(test_char)
    print(f"Original point: {original_point:.6f}")
    
    # Test spin and reverse
    test_time = 50
    spun_point = apply_spin(original_point, test_time)
    print(f"After spin (time={test_time}): {spun_point:.6f}")
    
    reversed_point = reverse_spin(spun_point, test_time)
    print(f"After reverse: {reversed_point:.6f}")
    print(f"Difference: {abs(reversed_point - original_point):.10f}")
    
    # Convert back to character
    recovered_char = point_to_char(reversed_point)
    print(f"Recovered character: {recovered_char} ('{chr(recovered_char) if recovered_char else 'None'}')")
    print()

def simulate_timing_accurately():
    """Simulate timing more accurately"""
    print("=== Simulating Timing ===")
    
    # Simulate the exact process from main.py
    fake_flag = b'tjctf{fake_flag}'
    times = []
    
    for i, char in enumerate(fake_flag):
        st = perf_counter()
        # Simulate the same computation
        point = sqrt(diam - (char * 31) ** 2)
        u = 0
        while u < 1000000:
            u += 1
        en = perf_counter()
        time = (en - st) * spin
        times.append(int(time))
        print(f"Char {i:2d} ('{chr(char)}'): time = {int(time)}")
    
    return times

def solve_with_timing(shifted_points, times):
    """Solve using specific timing values"""
    print(f"\n=== Solving with timing: {times} ===")
    
    flag_chars = []
    
    for i, shifted_point in enumerate(shifted_points):
        if i < len(times):
            time = times[i]
            try:
                # Reverse the spin
                original_point = reverse_spin(shifted_point, time)
                
                # Convert back to character
                char_val = point_to_char(original_point)
                
                if char_val is not None and 0 <= char_val <= 127:
                    char = chr(char_val)
                    flag_chars.append(char)
                    print(f"Point {i:2d}: {shifted_point:8.2f} -> {original_point:8.2f} -> {char_val:3d} -> '{char}' (time {time})")
                else:
                    flag_chars.append('?')
                    print(f"Point {i:2d}: {shifted_point:8.2f} -> {original_point:8.2f} -> invalid char (time {time})")
            except Exception as e:
                flag_chars.append('?')
                print(f"Point {i:2d}: {shifted_point:8.2f} -> ERROR: {e} (time {time})")
        else:
            flag_chars.append('?')
            print(f"Point {i:2d}: {shifted_point:8.2f} -> no timing data")
    
    return ''.join(flag_chars)

def brute_force_timing(shifted_points):
    """Try to find the best timing for each point"""
    print(f"\n=== Brute Force Timing ===")
    
    flag_chars = []
    best_times = []
    
    for i, shifted_point in enumerate(shifted_points):
        best_char = None
        best_diff = float('inf')
        best_time = None
        
        # Try all reasonable ASCII characters
        for char_val in range(32, 127):
            try:
                original_point = char_to_point(char_val)
                
                # Try different time values
                for time in range(10, 100):
                    spun_point = apply_spin(original_point, time)
                    diff = abs(spun_point - shifted_point)
                    
                    if diff < best_diff:
                        best_diff = diff
                        best_char = char_val
                        best_time = time
            except:
                continue
        
        if best_char is not None and best_diff < 1.0:
            char = chr(best_char)
            flag_chars.append(char)
            best_times.append(best_time)
            print(f"Point {i:2d}: {shifted_point:8.2f} -> '{char}' (time {best_time}, diff {best_diff:.6f})")
        else:
            flag_chars.append('?')
            best_times.append(0)
            print(f"Point {i:2d}: {shifted_point:8.2f} -> no good match")
    
    print(f"\nBest times found: {best_times}")
    return ''.join(flag_chars)

def main():
    # Test the math first
    test_math()
    
    # Read the shifted points
    with open("points.txt", 'r') as f:
        content = f.read().strip()
        shifted_points = ast.literal_eval(content)
    
    print(f"Loaded {len(shifted_points)} shifted points")
    
    # Simulate timing
    simulated_times = simulate_timing_accurately()
    
    # Try solving with simulated timing
    flag1 = solve_with_timing(shifted_points, simulated_times)
    print(f"\nFlag with simulated timing: {flag1}")
    
    # Try brute force approach
    flag2 = brute_force_timing(shifted_points)
    print(f"\nFlag with brute force timing: {flag2}")

if __name__ == "__main__":
    main()
