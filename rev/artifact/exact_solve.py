#!/usr/bin/env python3
"""
Exact solution using the actual timing values from main.py
"""

import ast
from math import pi, sqrt, asin, degrees, radians, sin

# Constants from main.py
circ = 24901
diam = (circ / (2 * pi)) ** 2 
rad = sqrt(diam)
spin = 1000

def find_ang(point):
    return degrees(asin(point / rad))

def reverse_single_spin(spun_point, time):
    """Reverse the spin operation for a single point"""
    ang = time / circ * 360
    current_ang = find_ang(spun_point)
    original_ang = current_ang - ang
    return sin(radians(original_ang)) * rad

def point_to_char(point):
    """Convert point back to character using exact reverse formula"""
    try:
        char_squared = (diam - point ** 2) / (31 ** 2)
        if char_squared >= 0:
            char_val = sqrt(char_squared)
            return int(round(char_val))
        else:
            return None
    except:
        return None

def main():
    print("=== Exact CTF Solution ===")
    
    # Read the shifted points
    with open("points.txt", 'r') as f:
        shifted_points = ast.literal_eval(f.read())
    
    # These are the actual timing values from running main.py
    actual_times = [82, 80, 71, 70, 68, 70, 72, 67, 66, 67, 76, 66, 81, 92, 75, 77]
    
    print(f"Using actual timing values: {actual_times}")
    print(f"Number of points: {len(shifted_points)}")
    print(f"Number of times: {len(actual_times)}")
    
    # The fake flag has 16 characters, but we have 35 points
    # This suggests the real flag is longer
    
    # Let's extend the timing pattern or use the available times cyclically
    extended_times = actual_times.copy()
    while len(extended_times) < len(shifted_points):
        extended_times.extend(actual_times)
    extended_times = extended_times[:len(shifted_points)]
    
    print(f"Extended timing values: {extended_times}")
    
    # Reverse each point using the exact timing
    print("\nReversing with exact timing:")
    flag_chars = []
    
    for i, shifted_point in enumerate(shifted_points):
        time = extended_times[i]
        
        try:
            original_point = reverse_single_spin(shifted_point, time)
            char_val = point_to_char(original_point)
            
            if char_val is not None and 0 <= char_val <= 127:
                char = chr(char_val)
                flag_chars.append(char)
                print(f"Point {i:2d}: {shifted_point:8.2f} -> {original_point:8.2f} -> {char_val:3d} -> '{char}' (time {time})")
            else:
                flag_chars.append('?')
                print(f"Point {i:2d}: {shifted_point:8.2f} -> {original_point:8.2f} -> invalid (time {time})")
        except Exception as e:
            flag_chars.append('?')
            print(f"Point {i:2d}: {shifted_point:8.2f} -> ERROR: {e} (time {time})")
    
    # Construct the flag
    flag = ''.join(flag_chars)
    print(f"\nFinal flag: {flag}")
    
    # Also try using just the first 16 timing values if the flag is shorter
    print(f"\n=== Alternative: Using only first 16 times ===")
    if len(shifted_points) >= 16:
        alt_flag_chars = []
        for i in range(16):
            time = actual_times[i]
            shifted_point = shifted_points[i]
            
            try:
                original_point = reverse_single_spin(shifted_point, time)
                char_val = point_to_char(original_point)
                
                if char_val is not None and 0 <= char_val <= 127:
                    char = chr(char_val)
                    alt_flag_chars.append(char)
                    print(f"Point {i:2d}: {shifted_point:8.2f} -> '{char}' (time {time})")
                else:
                    alt_flag_chars.append('?')
                    print(f"Point {i:2d}: {shifted_point:8.2f} -> invalid (time {time})")
            except:
                alt_flag_chars.append('?')
                print(f"Point {i:2d}: {shifted_point:8.2f} -> ERROR (time {time})")
        
        alt_flag = ''.join(alt_flag_chars)
        print(f"Alternative flag (16 chars): {alt_flag}")

if __name__ == "__main__":
    main()
