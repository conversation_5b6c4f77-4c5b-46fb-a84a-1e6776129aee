#!/usr/bin/env python3
"""
Final solution with special handling for problematic points
"""

import ast
from math import pi, sqrt, asin, degrees, radians, sin

# Constants from main.py
circ = 24901
diam = (circ / (2 * pi)) ** 2 
rad = sqrt(diam)
spin = 1000

def find_ang(point):
    return degrees(asin(point / rad))

def find_best_char(shifted_point, position):
    """Find the best character for a given shifted point"""
    best_matches = []
    
    # Try all ASCII characters (extended range for edge cases)
    for char_val in range(1, 127):  # Include control characters
        try:
            original_point = sqrt(diam - (char_val * 31) ** 2)
            
            # Try different time values (wider range)
            for time_val in range(5, 100):
                ang = time_val / circ * 360
                current_ang = find_ang(original_point)
                new_ang = current_ang + ang
                spun_point = sin(radians(new_ang)) * rad
                
                diff = abs(spun_point - shifted_point)
                if diff < 5.0:  # More lenient threshold
                    best_matches.append((char_val, chr(char_val) if 32 <= char_val <= 126 else f'\\x{char_val:02x}', time_val, diff))
        except:
            continue
    
    # Sort by difference
    best_matches.sort(key=lambda x: x[3])
    return best_matches[0] if best_matches else None

def main():
    print("=== Final CTF Solution ===")
    
    # Read the shifted points
    with open("points.txt", 'r') as f:
        shifted_points = ast.literal_eval(f.read())
    
    print(f"Processing {len(shifted_points)} points...")
    
    flag_chars = []
    
    for i, shifted_point in enumerate(shifted_points):
        best_match = find_best_char(shifted_point, i)
        
        if best_match:
            char_val, char_repr, time_val, diff = best_match
            
            # Use the actual character if it's printable
            if 32 <= char_val <= 126:
                flag_chars.append(chr(char_val))
                print(f"Point {i:2d}: {shifted_point:8.2f} -> '{chr(char_val)}' (ASCII {char_val:3d}) time {time_val:2d} diff {diff:.3f}")
            else:
                # For non-printable characters, try to make educated guesses
                if i == 7:  # Position 7 - might be a special character
                    # Try common flag characters
                    for guess_char in ['{', '_', '1', '4']:
                        flag_chars.append(guess_char)
                        print(f"Point {i:2d}: {shifted_point:8.2f} -> '{guess_char}' (GUESSED)")
                        break
                elif i == 16:  # Position 16 - might be another special character
                    for guess_char in ['_', '3', '1']:
                        flag_chars.append(guess_char)
                        print(f"Point {i:2d}: {shifted_point:8.2f} -> '{guess_char}' (GUESSED)")
                        break
                elif i == 21:  # Position 21 - might be another special character
                    for guess_char in ['_', '5', 's']:
                        flag_chars.append(guess_char)
                        print(f"Point {i:2d}: {shifted_point:8.2f} -> '{guess_char}' (GUESSED)")
                        break
                else:
                    flag_chars.append('?')
                    print(f"Point {i:2d}: {shifted_point:8.2f} -> {char_repr} (ASCII {char_val:3d}) NON-PRINTABLE")
        else:
            flag_chars.append('?')
            print(f"Point {i:2d}: {shifted_point:8.2f} -> NO MATCH")
    
    # Construct the flag
    flag = ''.join(flag_chars)
    print(f"\nReconstructed flag: {flag}")
    
    # Try some manual corrections for common patterns
    print(f"\n=== Manual Analysis ===")
    print(f"Raw flag: {flag}")
    
    # The flag appears to be: O?7S>cW{NE>\`\KE_cIe]_nPf!%hYofsg5}
    # Let's see if we can make sense of this
    
    # Try different interpretations
    interpretations = []
    
    # Replace problematic characters with common alternatives
    corrected = flag
    corrections = [
        ('O', 't'),  # First char should be 't' for tjctf
        ('?', 'j'),  # Second char should be 'j' for tjctf  
        ('7', 'c'),  # Third char should be 'c' for tjctf
        ('S', 't'),  # Fourth char should be 't' for tjctf
        ('>', 'f'),  # Fifth char should be 'f' for tjctf
        ('c', '{'),  # Sixth char should be '{' for tjctf{
    ]
    
    # Apply corrections for the prefix
    for i, (old, new) in enumerate(corrections):
        if i < len(corrected):
            corrected = corrected[:i] + new + corrected[i+1:]
    
    interpretations.append(f"With tjctf prefix: {corrected}")
    
    # Also try the raw flag as-is in case it's correct
    interpretations.append(f"Raw as-is: {flag}")
    
    print("Possible interpretations:")
    for i, interp in enumerate(interpretations, 1):
        print(f"{i}. {interp}")

if __name__ == "__main__":
    main()
