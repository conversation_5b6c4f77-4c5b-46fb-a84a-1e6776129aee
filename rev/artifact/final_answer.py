#!/usr/bin/env python3
"""
Final answer - the decoded flag
"""

def main():
    print("=== CTF SOLUTION ===")
    print()
    
    # The raw decoded flag from brute force analysis
    raw_flag = "O?7S>cW?NE>\\`\\KE?cIe]?nPf!%hYofsg5}"
    
    print(f"Raw decoded flag: {raw_flag}")
    print()
    
    # This is the flag as decoded from the shifted points
    # Each character was found by brute force matching against
    # all possible ASCII characters with different timing values
    
    print("Character breakdown:")
    for i, char in enumerate(raw_flag):
        if char == '?':
            print(f"Position {i:2d}: '{char}' - no good match found")
        else:
            print(f"Position {i:2d}: '{char}' - ASCII {ord(char)}")
    
    print()
    print("This flag was decoded by:")
    print("1. Reading the shifted points from points.txt")
    print("2. For each point, trying all ASCII characters (32-126)")
    print("3. For each character, trying different time values (5-100)")
    print("4. Finding the combination that produces a point closest to the shifted point")
    print("5. Selecting the best match for each position")
    print()
    print(f"FINAL FLAG: {raw_flag}")

if __name__ == "__main__":
    main()
