#!/usr/bin/env python3
"""
Final mathematical solution - work backwards from expected flag format
"""

import math
import ast

# Constants from main.py
circ = 24901
diam = (circ / (2 * math.pi)) ** 2
rad = math.sqrt(diam)
spin = 1000

def recover_char_from_point(point, time):
    """Recover character from point using exact mathematical reversal"""
    try:
        nang = math.degrees(math.asin(point / rad))
        ang = time / circ * 360
        original_ang = nang - ang
        p0 = rad * math.sin(math.radians(original_ang))
        
        char_times_31_squared = diam - p0 * p0
        if char_times_31_squared >= 0:
            char_times_31 = math.sqrt(char_times_31_squared)
            char_val = char_times_31 / 31
            char_code = int(round(char_val))
            
            if 0 <= char_code <= 127:
                return chr(char_code), char_code
            else:
                return None, char_code
        else:
            return None, None
            
    except Exception as e:
        return None, None

def find_timing_for_target_char(point, target_char):
    """Find timing value that produces the target character for a given point"""
    target_code = ord(target_char)
    
    for time in range(10, 150):
        char, char_code = recover_char_from_point(point, time)
        if char_code == target_code:
            return time
    
    return None

def main():
    print("=== Final Mathematical Solution ===")
    
    # Read the points
    with open("points.txt", 'r') as f:
        content = f.read().strip()
        points = ast.literal_eval(content)
    
    print(f"Loaded {len(points)} points")
    
    # Try to find timing values that produce "tjctf{" for the first 6 characters
    expected_start = "tjctf{"
    print(f"\nTrying to find timing for expected start: '{expected_start}'")
    
    found_times = []
    for i, (point, expected_char) in enumerate(zip(points[:6], expected_start)):
        timing = find_timing_for_target_char(point, expected_char)
        if timing:
            found_times.append(timing)
            print(f"Point {i}: {point:.2f} -> '{expected_char}' with timing {timing}")
        else:
            print(f"Point {i}: {point:.2f} -> '{expected_char}' NOT POSSIBLE")
            found_times.append(None)
    
    # If we found valid timings for the start, use the pattern
    if all(t is not None for t in found_times):
        print(f"\nFound timing pattern for 'tjctf{{': {found_times}")
        avg_timing = sum(found_times) / len(found_times)
        print(f"Average timing: {avg_timing:.1f}")
        
        # Use this timing for the entire flag
        print(f"\n=== Using discovered timing pattern ===")
        flag = ""
        for i, point in enumerate(points):
            # Use the average timing we discovered
            char, char_code = recover_char_from_point(point, avg_timing)
            if char:
                flag += char
                print(f"Point {i:2d}: {point:8.2f} -> '{char}' (ASCII {char_code})")
            else:
                flag += '?'
                print(f"Point {i:2d}: {point:8.2f} -> invalid (code {char_code})")
        
        print(f"\nFinal flag: {flag}")
    else:
        print("\nCould not find consistent timing for 'tjctf{' - trying best available results")
        
        # Use the best result from previous analysis
        # Strategy 3 looked most promising
        print("\nUsing Strategy 3 result as best guess:")
        best_flag = "OA6S>cW?ND=[a\\KE?cIf\\?mPf!$hYofrf5}"
        print(f"Best guess: {best_flag}")
        
        # Also try some manual corrections
        print(f"\nTrying manual corrections to make it look more flag-like:")
        
        # The result suggests the flag might be something like:
        # tjctf{...} but with different characters
        
        # Let's try a different approach - use the most common timing value
        fake_times = [64, 56, 56, 55, 55, 55, 71, 57, 58, 63, 66, 69, 68, 59, 70, 68]
        most_common_time = 55  # appears 3 times
        
        print(f"\nTrying with most common timing value: {most_common_time}")
        flag_common = ""
        for i, point in enumerate(points):
            char, char_code = recover_char_from_point(point, most_common_time)
            if char and 32 <= ord(char) <= 126:
                flag_common += char
            else:
                flag_common += '?'
        
        print(f"Most common timing result: {flag_common}")
        
        # Try with median timing
        sorted_times = sorted(fake_times)
        median_time = sorted_times[len(sorted_times)//2]
        
        print(f"\nTrying with median timing value: {median_time}")
        flag_median = ""
        for i, point in enumerate(points):
            char, char_code = recover_char_from_point(point, median_time)
            if char and 32 <= ord(char) <= 126:
                flag_median += char
            else:
                flag_median += '?'
        
        print(f"Median timing result: {flag_median}")
        
        print(f"\n=== ALL RESULTS ===")
        print(f"Strategy 3:      {best_flag}")
        print(f"Common timing:   {flag_common}")
        print(f"Median timing:   {flag_median}")

if __name__ == "__main__":
    main()
