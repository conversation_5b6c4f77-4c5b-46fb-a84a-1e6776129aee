#!/usr/bin/env python3
"""
Reverse solution following the clear plan
"""

import math
import ast

# Constants from main.py
circ = 24901
diam = (circ / (2 * math.pi)) ** 2
rad = math.sqrt(diam)
spin = 1000

def reverse_flag(points, times):
    """Reverse the transformation to recover the flag"""
    flag = ''
    
    for i, (pt, t) in enumerate(zip(points, times)):
        try:
            # Step 1: Get the post-spin angle
            angle = math.asin(pt / rad)
            
            # Step 2: Calculate the spin angle in degrees
            spin_deg = (t / circ) * 360
            
            # Step 3: Remove the spin to get original angle
            orig_angle_deg = math.degrees(angle) - spin_deg
            orig_angle = math.radians(orig_angle_deg)
            
            # Step 4: Get the original point
            orig_point = math.sin(orig_angle) * rad
            
            # Step 5: Solve for the ASCII character
            # orig_point = sqrt(diam - (char * 31)^2)
            # char = sqrt((diam - orig_point^2)) / 31
            char_val = math.sqrt(diam - orig_point ** 2) / 31
            char_code = int(round(char_val))
            
            if 0 <= char_code <= 127:
                char = chr(char_code)
                flag += char
                print(f"Point {i:2d}: {pt:8.2f} -> angle {math.degrees(angle):6.2f}° -> orig_angle {orig_angle_deg:6.2f}° -> orig_point {orig_point:8.2f} -> char {char_code:3d} ('{char}') [time {t}]")
            else:
                flag += '?'
                print(f"Point {i:2d}: {pt:8.2f} -> invalid char {char_code} [time {t}]")
                
        except Exception as e:
            flag += '?'
            print(f"Point {i:2d}: {pt:8.2f} -> ERROR: {e} [time {t}]")
    
    return flag

def main():
    print("=== Reverse Solution ===")
    
    # Read the points
    with open("points.txt", 'r') as f:
        content = f.read().strip()
        points = ast.literal_eval(content)
    
    print(f"Loaded {len(points)} points")
    
    # The timing values from running main.py (fake flag has 16 chars)
    fake_flag_times = [70, 72, 76, 75, 71, 80, 95, 77, 88, 71, 80, 90, 83, 97, 79, 71]
    
    print(f"Have {len(fake_flag_times)} timing values from fake flag")
    
    # Since we have 35 points but only 16 timing values, we need to figure out the pattern
    # Let's try a few approaches:
    
    # Approach 1: Use only the first 16 points (maybe the real flag is also 16 chars)
    print(f"\n=== Approach 1: First 16 points only ===")
    if len(points) >= 16:
        flag1 = reverse_flag(points[:16], fake_flag_times)
        print(f"Flag (first 16): {flag1}")
    
    # Approach 2: Repeat the timing pattern cyclically
    print(f"\n=== Approach 2: Repeat timing pattern ===")
    extended_times = []
    for i in range(len(points)):
        extended_times.append(fake_flag_times[i % len(fake_flag_times)])
    
    flag2 = reverse_flag(points, extended_times)
    print(f"Flag (repeated timing): {flag2}")
    
    # Approach 3: Try to simulate the actual timing for the real flag length
    print(f"\n=== Approach 3: Simulate timing for {len(points)} characters ===")
    
    # Simulate timing for a flag of the actual length
    from time import perf_counter
    simulated_times = []
    
    for i in range(len(points)):
        st = perf_counter()
        # Simulate the computation (dummy character)
        dummy_point = math.sqrt(diam - (100 * 31) ** 2)  # Use 'd' as dummy
        u = 0
        while u < 1000000:
            u += 1
        en = perf_counter()
        time = (en - st) * spin
        simulated_times.append(int(time))
    
    print(f"Simulated times: {simulated_times[:10]}... (showing first 10)")
    
    flag3 = reverse_flag(points, simulated_times)
    print(f"Flag (simulated timing): {flag3}")
    
    # Show all results
    print(f"\n=== RESULTS ===")
    print(f"Approach 1 (first 16): {flag1 if len(points) >= 16 else 'N/A'}")
    print(f"Approach 2 (repeated):  {flag2}")
    print(f"Approach 3 (simulated): {flag3}")

if __name__ == "__main__":
    main()
