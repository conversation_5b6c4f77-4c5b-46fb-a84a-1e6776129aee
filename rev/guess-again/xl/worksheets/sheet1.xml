<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<worksheet xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:xdr="http://schemas.openxmlformats.org/drawingml/2006/spreadsheetDrawing" xmlns:x14="http://schemas.microsoft.com/office/spreadsheetml/2009/9/main" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" mc:Ignorable="x14ac xr xr2 xr3" xmlns:x14ac="http://schemas.microsoft.com/office/spreadsheetml/2009/9/ac" xmlns:xr="http://schemas.microsoft.com/office/spreadsheetml/2014/revision" xmlns:xr2="http://schemas.microsoft.com/office/spreadsheetml/2015/revision2" xmlns:xr3="http://schemas.microsoft.com/office/spreadsheetml/2016/revision3" xr:uid="{F18CCB61-5DE3-404D-B2BE-05E0CB1C1A61}"><sheetPr codeName="Sheet1"/><dimension ref="A1"/><sheetViews><sheetView tabSelected="1" zoomScale="131" workbookViewId="0"><selection activeCell="E7" sqref="E7"/></sheetView></sheetViews><sheetFormatPr baseColWidth="10" defaultRowHeight="16" x14ac:dyDescent="0.2"/><sheetData/><pageMargins left="0.7" right="0.7" top="0.75" bottom="0.75" header="0.3" footer="0.3"/><drawing r:id="rId1"/><legacyDrawing r:id="rId2"/><mc:AlternateContent xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"><mc:Choice Requires="x14"><controls><mc:AlternateContent xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"><mc:Choice Requires="x14"><control shapeId="1025" r:id="rId3" name="Button 1"><controlPr defaultSize="0" print="0" autoFill="0" autoPict="0" macro="[0]!CheckFlag"><anchor moveWithCells="1" sizeWithCells="1"><from><xdr:col>1</xdr:col><xdr:colOff>50800</xdr:colOff><xdr:row>5</xdr:row><xdr:rowOff>127000</xdr:rowOff></from><to><xdr:col>2</xdr:col><xdr:colOff>749300</xdr:colOff><xdr:row>7</xdr:row><xdr:rowOff>101600</xdr:rowOff></to></anchor></controlPr></control></mc:Choice></mc:AlternateContent></controls></mc:Choice></mc:AlternateContent></worksheet>