<xml xmlns:v="urn:schemas-microsoft-com:vml"
 xmlns:o="urn:schemas-microsoft-com:office:office"
 xmlns:x="urn:schemas-microsoft-com:office:excel">
 <o:shapelayout v:ext="edit">
  <o:idmap v:ext="edit" data="1"/>
 </o:shapelayout><v:shapetype id="_x0000_t201" coordsize="21600,21600" o:spt="201"
  path="m,l,21600r21600,l21600,xe">
  <v:stroke joinstyle="miter"/>
  <v:path shadowok="f" o:extrusionok="f" strokeok="f" fillok="f" o:connecttype="rect"/>
  <o:lock v:ext="edit" shapetype="t"/>
 </v:shapetype><v:shape id="_x0000_s1025" type="#_x0000_t201" style='position:absolute;
  margin-left:69pt;margin-top:90pt;width:120pt;height:30pt;z-index:1;
  mso-wrap-style:tight' o:button="t" fillcolor="#f0f0f0 [67]" strokecolor="black [64]"
  o:insetmode="auto">
  <v:fill color2="#f0f0f0 [67]" o:detectmouseclick="t"/>
  <o:lock v:ext="edit" rotation="t"/>
  <v:textbox o:singleclick="f">
   <div style='text-align:center'><font face="Aptos Narrow" size="300"
   color="auto">Check Flag</font></div>
  </v:textbox>
  <x:ClientData ObjectType="Button">
   <x:Anchor>
    1, 4, 5, 10, 2, 59, 7, 8</x:Anchor>
   <x:PrintObject>False</x:PrintObject>
   <x:AutoFill>False</x:AutoFill>
   <x:FmlaMacro>[0]!CheckFlag</x:FmlaMacro>
   <x:TextHAlign>Center</x:TextHAlign>
   <x:TextVAlign>Center</x:TextVAlign>
  </x:ClientData>
 </v:shape></xml>