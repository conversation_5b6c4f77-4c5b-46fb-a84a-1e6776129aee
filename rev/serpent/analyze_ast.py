#!/usr/bin/env python3
import pickle
import ast
import sys

def analyze_ast_node(node, depth=0, max_depth=None):
    """Recursively analyze AST node and print structure"""
    indent = "  " * depth
    
    if max_depth is not None and depth > max_depth:
        return
    
    node_type = type(node).__name__
    print(f"{indent}{node_type}", end="")
    
    # Print relevant attributes for different node types
    if hasattr(node, 'name'):
        print(f" (name: {node.name})", end="")
    elif hasattr(node, 'id'):
        print(f" (id: {node.id})", end="")
    elif hasattr(node, 'value') and isinstance(node.value, (str, int, float, bool)):
        print(f" (value: {repr(node.value)})", end="")
    elif hasattr(node, 'attr'):
        print(f" (attr: {node.attr})", end="")
    elif hasattr(node, 'op'):
        print(f" (op: {type(node.op).__name__})", end="")
    
    print()
    
    # Recursively process child nodes
    for field, value in ast.iter_fields(node):
        if isinstance(value, list):
            for i, item in enumerate(value):
                if isinstance(item, ast.AST):
                    print(f"{indent}  {field}[{i}]:")
                    analyze_ast_node(item, depth + 2, max_depth)
        elif isinstance(value, ast.AST):
            print(f"{indent}  {field}:")
            analyze_ast_node(value, depth + 2, max_depth)

def convert_ast_to_code(node):
    """Convert AST back to Python code"""
    try:
        return ast.unparse(node)
    except:
        # Fallback for older Python versions
        import astor
        return astor.to_source(node)

def find_deepest_flag(node, depth=0, path=""):
    """Find the flag at the deepest level of nesting"""
    max_depth = 0
    deepest_flag = None
    deepest_path = ""

    # Check if this node contains a flag
    if hasattr(node, 'value') and isinstance(node.value, str) and 'tjctf{' in node.value:
        if 'f0ggy_d4ys' in node.value:  # This seems to be at the deepest level
            return depth, node.value, path

    # Recursively search child nodes
    for field, value in ast.iter_fields(node):
        if isinstance(value, list):
            for i, item in enumerate(value):
                if isinstance(item, ast.AST):
                    child_depth, child_flag, child_path = find_deepest_flag(item, depth + 1, f"{path}.{field}[{i}]")
                    if child_depth > max_depth:
                        max_depth = child_depth
                        deepest_flag = child_flag
                        deepest_path = child_path
        elif isinstance(value, ast.AST):
            child_depth, child_flag, child_path = find_deepest_flag(value, depth + 1, f"{path}.{field}")
            if child_depth > max_depth:
                max_depth = child_depth
                deepest_flag = child_flag
                deepest_path = child_path

    return max_depth, deepest_flag, deepest_path

def main():
    # Load the pickled AST
    with open('ast_dump.pickle', 'rb') as f:
        ast_module = pickle.load(f)

    print("=== Finding the deepest flag ===")
    depth, flag, path = find_deepest_flag(ast_module)

    if flag:
        print(f"Found flag at depth {depth}: {flag}")
        print(f"Path: {path}")
    else:
        print("No flag found")

    # Let's also execute the code to see what happens
    print("\n=== Executing the converted code ===")
    try:
        code = ast.unparse(ast_module)

        # Execute the code
        exec(code)

        # Try to instantiate and navigate to the deepest level
        print("\n=== Navigating to deepest level ===")
        outer = OuterLayer()
        result = outer.process_data("test")

        # Navigate through the nested structure
        level_one = result
        middle_layer = level_one
        deep_result = middle_layer.deep_process()
        inner_layer = deep_result
        ultra_result = inner_layer.ultra_deep_process()
        core_layer = ultra_result
        final_result = core_layer.final_process()
        deepest_layer = final_result
        ultimate_result = deepest_layer.ultimate_process()

        print(f"Ultimate result: {ultimate_result}")

    except Exception as e:
        print(f"Error executing code: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
