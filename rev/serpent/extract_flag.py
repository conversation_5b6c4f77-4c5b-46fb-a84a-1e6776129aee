#!/usr/bin/env python3
import pickle
import ast

def find_deepest_flag(node, depth=0):
    """Find the flag at the deepest level of nesting"""
    max_depth = 0
    deepest_flag = None
    
    # Check if this node contains a flag
    if hasattr(node, 'value') and isinstance(node.value, str) and 'tjctf{' in node.value:
        if 'f0ggy_d4ys' in node.value:  # This seems to be at the deepest level
            return depth, node.value
    
    # Recursively search child nodes
    for field, value in ast.iter_fields(node):
        if isinstance(value, list):
            for item in value:
                if isinstance(item, ast.AST):
                    child_depth, child_flag = find_deepest_flag(item, depth + 1)
                    if child_depth > max_depth:
                        max_depth = child_depth
                        deepest_flag = child_flag
        elif isinstance(value, ast.AST):
            child_depth, child_flag = find_deepest_flag(value, depth + 1)
            if child_depth > max_depth:
                max_depth = child_depth
                deepest_flag = child_flag
    
    return max_depth, deepest_flag

def main():
    # Load the pickled AST
    with open('ast_dump.pickle', 'rb') as f:
        ast_module = pickle.load(f)
    
    print("=== Finding the deepest flag ===")
    depth, flag = find_deepest_flag(ast_module)
    
    if flag:
        print(f"Found flag at depth {depth}: {flag}")
        print(f"\nThe flag is: {flag}")
    else:
        print("No flag found")

if __name__ == "__main__":
    main()
