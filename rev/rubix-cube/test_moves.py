from rubixcube import RubiksCube

def test_inverse_moves():
    """Test that our inverse moves work correctly"""
    cube = RubiksCube()
    
    # Test each move and its inverse
    moves_to_test = [
        ("U", "U'"),
        ("D", "D'"),
        ("L", "L'"),
        ("R", "R'"),
        ("F", "F'"),
        ("B", "B'")
    ]
    
    for move, inverse in moves_to_test:
        # Save original state
        original_state = cube.get_cube()
        
        # Apply move then inverse
        cube.apply_moves(move)
        cube.apply_moves(inverse)
        
        # Check if we're back to original state
        if cube.get_cube() == original_state:
            print(f"✓ {move} and {inverse} are correct inverses")
        else:
            print(f"✗ {move} and {inverse} are NOT correct inverses")

if __name__ == "__main__":
    test_inverse_moves()
