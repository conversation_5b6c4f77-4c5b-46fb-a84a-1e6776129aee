# TJCTF 2024 - Packet Palette (Forensics)

**Challenge:** Packet Palette  
**Category:** Forensics  
**Points:** 392  
**Solves:** 76  

## Challenge Description

Someone tried to reinvent USB-over-IP… poorly. Can you sift through their knockoff protocol?

**Files:** `chall.pcapng`

## Solution Overview

This challenge involves analyzing a network packet capture file containing a custom protocol that transmits a PNG image. We need to reverse engineer the protocol and extract the image to find the flag.

## Step 1: Initial Analysis

First, let's examine what we have:

```bash
file chall.pcapng
```

This confirms it's a packet capture file. Let's use `tshark` to get a quick overview:

```bash
tshark -r chall.pcapng -c 10
```

**Output:**
```
1   0.000000   ********** → **********   TCP 72 31337 → 31337 [PSH, ACK] Seq=1 Ack=1 Win=8192 Len=18
2   0.000373   ********** → **********   TCP 566 [TCP Previous segment not captured] 31337 → 31337 [PSH, ACK] Seq=1001 Ack=2 Win=8192 Len=512
...
```

**Key Observations:**
- Communication between `**********` and `**********`
- Using TCP port `31337` (a common "elite" port in CTFs)
- Multiple packets with 512-byte payloads

Let's check the total number of packets:

```bash
tshark -r chall.pcapng | wc -l
# Output: 23
```

Only 23 packets total, so this is manageable to analyze.

## Step 2: Protocol Analysis

Let's examine the packet contents in hex format:

```bash
tshark -r chall.pcapng -x
```

Looking at the first few packets, we can see:

**Packet 1:**
```
0030  20 00 c4 39 00 00 55 53 42 49 50 3a 44 45 56 49    ..9..USBIP:DEVI
0040  43 45 5f 52 45 41 44 59                           CE_READY
```

**Packet 2:**
```
0030  20 00 0c 07 00 00 55 53 42 49 00 00 00 15 00 00    .....USBI......
0040  01 f4 89 50 4e 47 0d 0a 1a 0a 00 00 00 0d 49 48   ...PNG........IH
```

**Last Packet:**
```
0030  20 00 71 d2 00 00 55 53 42 49 50 3a 44 4f 4e 45    .q...USBIP:DONE
```

**Protocol Structure Discovered:**
1. **Handshake:** `USBIP:DEVICE_READY`
2. **Data packets:** `USBI` + metadata + payload
3. **End marker:** `USBIP:DONE`

**Important Discovery:** In packet 2, we see `89 50 4E 47` which is the PNG file signature! This means a PNG image is being transmitted.

## Step 3: Understanding the USBI Protocol

Looking at the USBI packets more carefully:

```
USBI + [2 bytes sequence] + [2 bytes unknown] + [4 bytes length] + [payload data]
```

For example, in packet 2:
- `USBI` - Protocol identifier
- `00 00` - Sequence number (0)
- `00 15` - Unknown field
- `00 00 01 f4` - Length (500 bytes)
- `89 50 4E 47...` - PNG data starts here

## Step 4: Writing the Extraction Script

Now we need to extract and reassemble the PNG data. Create a file called `extract_png.py`:

```python
#!/usr/bin/env python3

import struct
from scapy.all import *

def extract_usbi_data(pcap_file):
    """Extract data from USBI packets and reconstruct the PNG file"""
    packets = rdpcap(pcap_file)
    usbi_data = {}

    for packet in packets:
        if packet.haslayer(Raw):
            payload = bytes(packet[Raw])

            # Look for USBI header
            if payload.startswith(b'USBI'):
                # Parse the USBI header
                if len(payload) >= 12:
                    # Extract sequence number (first 2 bytes after USBI)
                    seq_num = struct.unpack('>H', payload[4:6])[0]
                    # Extract length (4 bytes starting at offset 8)
                    length = struct.unpack('>I', payload[8:12])[0]
                    # Extract the actual data
                    data = payload[12:12+length]

                    print(f"Packet {seq_num}: {len(data)} bytes")
                    usbi_data[seq_num] = data

    # Reconstruct by concatenating in sequence order
    reconstructed_data = b''
    for seq_num in sorted(usbi_data.keys()):
        reconstructed_data += usbi_data[seq_num]

    return reconstructed_data

def main():
    print("Extracting PNG from USBI packets...")
    png_data = extract_usbi_data('chall.pcapng')

    print(f"Total data: {len(png_data)} bytes")

    # Verify PNG format
    if png_data.startswith(b'\x89PNG'):
        print("✓ Valid PNG header found!")

        with open('flag.png', 'wb') as f:
            f.write(png_data)
        print("✓ Saved as flag.png")
    else:
        print("❌ Invalid PNG data")

if __name__ == '__main__':
    main()
```

**Key Points About the Script:**
- Uses Scapy to parse the packet capture
- Identifies USBI packets by looking for the "USBI" header
- Extracts sequence numbers and data lengths from the protocol
- Reassembles data in the correct order
- Validates PNG format by checking magic bytes

## Step 5: Running the Script

First, install the required Python library:

```bash
pip install scapy
```

Then run the extraction script:

```bash
python3 extract_png.py
```

**Expected Output:**
```
Extracting PNG from USBI packets...
Packet 0: 500 bytes
Packet 1: 500 bytes
Packet 2: 500 bytes
...
Packet 20: 375 bytes
Total data: 10375 bytes
✓ Valid PNG header found!
✓ Saved as flag.png
```

## Step 6: Getting the Flag

Open the extracted PNG file to see the flag:

```bash
# Linux
xdg-open flag.png

# macOS
open flag.png

# Windows
start flag.png
```

The image will display the flag in the format: `tjctf{...}`

## What We Learned

This challenge taught us several important concepts:

### 1. Custom Protocol Analysis
- How to identify protocol patterns in network traffic
- Understanding packet structure and sequencing
- Recognizing when data is being transmitted across multiple packets

### 2. File Format Recognition
- PNG files start with specific magic bytes: `\x89PNG`
- File signatures help identify data types in network streams
- Proper file reconstruction requires maintaining data integrity

### 3. Binary Data Handling
- Using Python's `struct` module to parse binary protocols
- Understanding big-endian vs little-endian byte ordering
- Converting raw bytes to meaningful data structures

### 4. Forensics Methodology
- Start with high-level analysis (packet counts, protocols)
- Drill down into specific packet contents
- Look for patterns and known file signatures
- Validate reconstructed data

## Tools and Dependencies

### Required Tools:
- `tshark` or `Wireshark` - For packet analysis
- `Python 3` - For scripting
- `scapy` library - For packet parsing

### Installation:
```bash
# Install tshark (part of Wireshark)
sudo apt-get install tshark  # Ubuntu/Debian
brew install wireshark       # macOS

# Install Python dependencies
pip install scapy
```

## Alternative Solutions

### Method 1: Manual Wireshark Analysis
1. Open `chall.pcapng` in Wireshark
2. Filter for packets containing "USBI"
3. Manually extract hex data from each packet
4. Concatenate the data and save as PNG

### Method 2: Command Line with tshark
```bash
# Extract raw packet data
tshark -r chall.pcapng -T fields -e data.data > raw_data.txt

# Process with custom script to extract USBI payloads
```

### Method 3: Python with Raw Socket Parsing
Instead of Scapy, use Python's built-in socket libraries for more control over packet parsing.

## Common Pitfalls

1. **Incorrect Byte Order:** Make sure to use big-endian (`>`) format for network protocols
2. **Missing Packets:** Verify all sequence numbers are present
3. **Wrong Offset:** Protocol headers can vary - always verify field positions
4. **File Corruption:** Check both PNG header and footer for complete reconstruction

## Flag Format
The flag follows the standard TJCTF format: `tjctf{...}`

## Conclusion

This challenge demonstrates a realistic scenario where attackers might use custom protocols to exfiltrate data. The key skills developed include:

- Reverse engineering unknown network protocols
- Reconstructing files from network traffic
- Using Python for forensics automation
- Understanding common file formats and signatures

These skills are essential for network forensics, malware analysis, and incident response in cybersecurity.
